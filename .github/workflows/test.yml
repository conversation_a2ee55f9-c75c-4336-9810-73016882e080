name: Test

on:
  push:
    branches-ignore:
      - devel
      - main
    tags-ignore:
      - '**'

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

env:
  total-runners: 5
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  lint:
    runs-on: [self-hosted, linux, X64, k8s]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          check-latest: true
          scope: '@xepelinapp'
      - name: Install dependencies
        run: npm install
      - name: Running lint check
        run: npm run lint:check

  test:
    runs-on: [self-hosted, linux, X64, k8s]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          check-latest: true
          scope: '@xepelinapp'
      - name: Install dependencies
        run: npm install
      - name: Running tests with coverage
        run: npm run test:coverage

  build:
    runs-on: [self-hosted, linux, X64, k8s]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          check-latest: true
          scope: '@xepelinapp'
      - name: Install dependencies
        run: npm install
      - name: Building service
        run: npm run build
