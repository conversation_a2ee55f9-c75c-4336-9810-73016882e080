name: '🔥 Trigger update main branch'

on:
  workflow_dispatch:
    inputs:
      fullDeploy:
        description: '<PERSON>ra commit a main con los últimos cambios en devel'
        required: true
        default: 'si'
        type: choice
        options:
          - 'no'
          - 'si'

jobs:
  trigger-update-main:
    name: trigger-update-main
    runs-on: [self-hosted, linux, X64, k8s]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: devel

      - name: Git config
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Release Manager"

      - name: Git switch to main
        run: git switch main

      - name: Git merge
        run: git merge devel
        if: ${{ inputs.fullDeploy == 'si' }}

      - name: Git push
        run: git push origin main
        if: ${{ inputs.fullDeploy == 'si' }}
