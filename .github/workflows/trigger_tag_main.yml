name: 'Tag changes on main branch'

on:
  workflow_run:
    workflows: ['🔥 Trigger update main branch']
    types:
      - completed

jobs:
  create_release:
    name: CreateRelease
    runs-on: [self-hosted, linux, X64, k8s]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main

      - name: Git config
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Release Manager"

      - name: Git tag
        run: git tag $(TZ=UTC date +%Y%m%d_%H%M%SZ)

      - name: Git push
        run: git push --tags origin main
