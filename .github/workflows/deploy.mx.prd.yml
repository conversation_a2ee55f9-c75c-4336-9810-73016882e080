name: '🚀 Deploy: Production'

on:
  workflow_run:
    workflows: ['Tag changes on main branch']
    types:
      - completed
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag en main'
        required: false
        default: ''

permissions:
  id-token: write
  contents: read
  packages: read
  pull-requests: write

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: [self-hosted, linux, X64, k8s]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup node
        uses: ./.github/actions/setup
        with:
          npm_token: ${{ secrets.NPM_TOKEN }}

      - name: Build code
        uses: ./.github/actions/build

      - name: Deploy
        uses: ./.github/actions/deploy
        with:
          environment: prd
          role_name: blackops-sat69-role-session-prd
          token: ${{ secrets.NPM_TOKEN }}
          gh_token: '${{ secrets.GITHUB_TOKEN }}'
          region: ${{ vars.AWS_REGION }}
          pulumi_token: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          assume_role: ${{ vars.PROD_ASSUME_ROLE }}
          bucket_name: ${{ vars.PROD_PULUMI_BUCKET }}
      - name: Get Outputs of infra
        id: infra
        working-directory: infra
        run: |
          set +e

          apiPlatformOutput=$(pulumi stack output apiPlatformOutput -s prd) 2>/dev/null

          if [[ $? == 0 ]]; then
            echo "apiPlatformOutput=$apiPlatformOutput" >> "$GITHUB_OUTPUT"
          fi

        env:
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Define JSON Schema
        id: json
        run: |
          json=$(cat <<EOF
          ${{ steps.infra.outputs.apiPlatformOutput || '[]'}}
          EOF
          )
          json_string=$(jq --raw-output '. | @json' <<< $json)
          echo "apiPlatformOutput=$json_string" >> "$GITHUB_OUTPUT"

    outputs:
      apiPlatformOutput: ${{ steps.json.outputs.apiPlatformOutput }}
  subscribe:
    name: '🚀 Api Subscribe'
    needs: [deploy]
    uses: xepelinapp/api-platform/.github/workflows/reusable-subscriber.yml@main
    with:
      data: '${{ needs.deploy.outputs.apiPlatformOutput }}'
      user-name: 'Blackops'
      user-email: '<EMAIL>'
      repository: ${{ github.repository }}
      commit: ${{ github.sha }}
    secrets: inherit
