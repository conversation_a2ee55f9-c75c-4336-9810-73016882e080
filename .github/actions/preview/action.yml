name: Deploy
description: Deploy project

inputs:
  token:
    description: NPM token
    required: true
  gh_token:
    description: Github token
    required: true
  region:
    description: AWS region
    required: true
  role_name:
    description: Session rolename
    required: true
  pulumi_token:
    description: Pulumi access token
    required: true
  assume_role:
    description: Assume role
    required: true
  bucket_name:
    description: Bucket name
    required: true

runs:
  using: composite
  steps:
    - name: Checkout Infra Policy repo
      uses: actions/checkout@v4
      with:
        repository: xepelinapp/platform-iac
        token: "${{ inputs.gh_token }}"
        path: tmp
        sparse-checkout: pulumi
        sparse-checkout-cone-mode: false

    - name: Move Policies to Pulumi working directory
      shell: bash
      run: |
        mv tmp/pulumi/policies infra/policies

    - name: Install dependencies
      shell: bash
      run: |
        cd infra/policies/packs
        echo @xepelinapp:registry=https://npm.pkg.github.com >> .npmrc
        echo //npm.pkg.github.com/:_authToken=$NPM_TOKEN >> .npmrc
        npm install
      env:
        NPM_TOKEN: ${{ inputs.token }}

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-region: ${{ inputs.region }}
        role-to-assume: ${{ inputs.assume_role }}
        role-session-name: ${{ inputs.role_name }}

    - name: Running pulumi
      uses: pulumi/actions@v4
      with:
        cloud-url: "s3://${{ inputs.bucket_name }}/sat69-scrapper"
        work-dir: infra
        stack-name: dev
        command: preview
        comment-on-pr: true
        upsert: true
        policyPacks: policies/packs
        policyPackConfigs: policies/config.json
      env:
        PULUMI_CONFIG_PASSPHRASE: "${{ inputs.pulumi_token }}"
        NPM_TOKEN: "${{ inputs.token }}"
