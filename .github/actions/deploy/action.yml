name: Deploy
description: Deploy project

inputs:
  token:
    description: NPM token
    required: true
  gh_token:
    description: Github token
    required: true
  environment:
    description: Pulumi enviroment
    required: true
  region:
    description: AWS region
    required: true
  role_name:
    description: Session rolename
    required: true
  pulumi_token:
    description: Pulumi access token
    required: true
  assume_role:
    description: Assume role
    required: true
  bucket_name:
    description: Bucket name
    required: true

runs:
  using: composite
  steps:
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-region: ${{ inputs.region }}
        role-to-assume: ${{ inputs.assume_role }}
        role-session-name: ${{ inputs.role_name }}

    - name: Running pulumi
      uses: pulumi/actions@v5
      with:
        cloud-url: "s3://${{ inputs.bucket_name }}/sat69-scrapper"
        work-dir: infra
        stack-name: "${{ inputs.environment }}"
        command: up
        refresh: true
        comment-on-pr: true
        upsert: true
      env:
        PULUMI_CONFIG_PASSPHRASE: "${{ inputs.pulumi_token }}"
        NPM_TOKEN: "${{ inputs.token }}"
