name: Installation
description: Install Dependencies & Use Node.js 20.x

inputs:
  npm_token:
    description: "NPM Token"
    required: true

runs:
  using: composite
  steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x

    - name: Install Dependencies
      shell: bash
      run: npm install
      env:
        NPM_TOKEN: ${{ inputs.npm_token }}
