# -*- mode: yaml -*-
manifest:
  version: 1.0

automations:
  estimated_time_to_review:
    if:
      - true
    run:
      - action: add-label@v1
        args:
          label: "{{ calc.etr }} min review"
          color: {{ 'E94637' if (calc.etr >= 20) else ('FBBD10' if (calc.etr >= 5) else '36A853') }}
          
  safe_changes:
    if:
      - {{ files | allDocs }}
    run: 
      - action: add-label@v1
        args:
          label: 'safe-changes'
      # You can uncomment the following action to get gitStream to automatically approve 
      # - action: approve@v1
      
  code_experts:
    if: 
      - true
    run:
      - action: add-comment@v1
        args:
          comment: |
            {{ repo | explainCodeExperts(gt=10) }}
  no_tests:
    if:
      - {{ files | match(regex=r/(test|spec)/) | nope }}
    run: 
      - action: add-label@v1
        args:
          label: 'missing-tests'
          color: '#E94637'

calc:
  etr: {{ branch | estimatedReviewTime }}



