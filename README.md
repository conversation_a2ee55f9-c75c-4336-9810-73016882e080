# Sat69 PDF download, parse and return service

## Configuration

> **_NOTE:_** ALL THE NAMES MUST BE SEPARATED BY DASHES (-)

### Repository Name

`${SQUAD_NAME}-${SERVICE_NAME}` for example: `blackops-mimetus`

### Environment Variables

- Parameter Store name format:

  - `/${STAGE}/${SERVICE_NAME}/${PARAMETER_NAME}` for parameters within a stage, for example `${ssm:/${self:provider.stage}/${self:service}/PARAMETER_NAME}`
  - `/${STAGE}/${PARAMETER_NAME}` for parameters without a stage, for example `${ssm:/${self:provider.stage}/PARAMETER_NAME}`

- Environment Variable calling format:
  `${env:VARIABLE, ssm:/${self:provider.stage}/${self:service}/VARIABLE}`

### Resource Naming

`${STAGE}-${SERVICE_NAME}-${RESOURCE_NAME}-${RESOURCE_TYPE}` for example: `'${self:provider.stage}-${self:service}-extractor-2extra-large-sqs`

### dependabot

Change reviewers of the pull requests as needed on `.github/dependabot.yml`, for example:

```yml
version: 2
updates:
  - package-ecosystem: npm
    directory: '/'
    schedule:
      interval: monthly
    open-pull-requests-limit: 10
    reviewers:
      - ${GITHUB_USERNAME}
      - ${GITHUB_USERNAME}
```

## Deployment

There are 2 deployments workflow scripts in `./github`, for development and production environments as example. You can uncomment them and modify them as needed.

## Quick Start

Install Node Version Manager [NVM](https://github.com/nvm-sh/nvm).

Install node v20

```bash
nvm install v20
nvm use v20
```

install deps

```bash
npm install
```

run example functions

```bash
npm run checkSat69:sqs
```

run tests

```bash
npm run test
npm run test:coverage
```

#### retrievePdf Example

```json
{
  "businessId": "1234"
  "json": true
}
```
