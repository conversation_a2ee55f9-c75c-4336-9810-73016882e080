paths:
  /hello:
    post:
      tags:
        - HelloWorld
      summary: Greets with a hello world
      requestBody:
        description: name details
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'BlackOps'
      responses:
        '200':
          description: returns a greeting
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Hello BlackOps'
        '422':
          $ref: '../components.yml#components/responses/ValidationError'
      security:
        - bearerAuth: []
