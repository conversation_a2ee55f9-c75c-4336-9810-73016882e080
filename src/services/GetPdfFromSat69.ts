import fs from 'fs';

import { Logger } from '@xepelinapp/blackops-commons-logger';
import { Page } from '@xepelinapp/blackops-commons-puppeteer';

import { EmptyDocumentError, GetPdfError } from '!libs/errors';

export class GetPdfFromSat69 {
  private logger;
  private urlSat69;

  constructor(urlSat69: string, logger: Logger) {
    this.logger = logger;
    this.urlSat69 = urlSat69;
  }

  FILE_SIZE_LIMIT = 10 * 1024; // 10KB

  handleFileCreation = (user: string, data: Buffer): void => {
    const tmpDir = '/tmp';
    if (!fs.existsSync(tmpDir)) {
      fs.mkdirSync(tmpDir);
    }

    const filePath = `${tmpDir}/${user}.pdf`;
    fs.writeFileSync(filePath, data);
  };

  public async execute(page: Page, user: string): Promise<void> {
    const reg = new RegExp('src="data:application/pdf;base64,(.*)"></iframe>');

    const MAX_RETRIES = 5;

    try {
      let tries = 1;
      while (tries <= MAX_RETRIES) {
        try {
          this.logger.info({
            message: 'Getting PDF from Sat69',
            identifier: user,
          });

          await page.goto(this.urlSat69, {
            timeout: Number(process.env.PUPPETEER_TIMEOUT) || 60000,
          });
          await page.waitForNavigation({
            waitUntil: 'load',
            timeout: Number(process.env.PUPPETEER_TIMEOUT) || 60000,
          });

          this.logger.debug({
            message: 'wait for iframe[title=pdfReporteOpinion',
          });
          await page.waitForSelector('iframe[title=pdfReporteOpinion]', {
            timeout: Number(process.env.PUPPETEER_TIMEOUT) || 60000,
          });
          break;
        } catch (e) {
          this.logger.debug({
            message: `Error waiting for iframe[title=pdfReporteOpinion]: ${tries} retires of ${String(MAX_RETRIES)}`,
          });
          this.logger.externalError(e);
          tries++;
        }
      }

      if (tries > MAX_RETRIES) {
        throw new GetPdfError(user);
      }

      const bodyHTML = await page.evaluate(() => document.documentElement.outerHTML);
      const pdf64 = bodyHTML.match(reg)?.[1] || '';

      if (!pdf64) {
        throw new EmptyDocumentError(user);
      }

      this.handleFileCreation(user, Buffer.from(pdf64, 'base64'));
      this.logger.info({
        message: 'PDF retrieved from Sat69',
        identifier: user,
      });
    } catch (error) {
      this.logger.debug({
        message: 'Error retrieving PDF from Sat 69',
      });
      this.logger.error(error);
      if (error instanceof EmptyDocumentError) {
        throw error;
      } else {
        throw new GetPdfError(user);
      }
    }
  }
}
