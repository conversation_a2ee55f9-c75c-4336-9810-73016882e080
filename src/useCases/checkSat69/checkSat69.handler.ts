import { randomUUID } from 'crypto';

import PromisePool from '@supercharge/promise-pool';
import { Context, SQSEvent, SQSBatchResponse, SQSBatchItemFailure } from 'aws-lambda';

import { initLogger } from '!libs/logger';
import { convertEventToDTO } from '!useCases/checkSat69/checkSat69.mapper';
import { CheckSat69UseCase } from '!useCases/checkSat69/checkSat69.useCase';

export const checkSat69 = async (event: SQSEvent, context: Context): Promise<SQSBatchResponse> => {
  const logger = initLogger(context, context.functionName);
  const { Records: records = [] } = event;
  const batchItemFailures: SQSBatchItemFailure[] = [];
  let skipped = 0;

  const { results } = await PromisePool.for(records)
    .withConcurrency(10)
    .handleError((error, { messageId }) => {
      if (error.constructor.name === 'IdentifierNotFoundError') {
        skipped += 1;
      } else {
        batchItemFailures.push({ itemIdentifier: messageId });
      }
      logger.error(error);
    })
    .process(async (record) => {
      const processLogger = logger.cloneInstance(randomUUID());

      processLogger.info(record);
      const dto = convertEventToDTO(record);
      const useCase = new CheckSat69UseCase(processLogger);
      await useCase.execute(dto);
    });

  logger.info({ results: results.length, batchItemFailures: batchItemFailures.length, skipped });

  return {
    batchItemFailures,
  };
};
