import { S3 } from '@xepelinapp/blackops-commons-aws-lambda';
import {
  Credentials<PERSON>anager,
  Sat69Cookie,
} from '@xepelinapp/blackops-commons-credentials-manager-lambda';
import { Logger } from '@xepelinapp/blackops-commons-logger';
import {
  Puppeteer,
  Page,
  TimeoutError,
  ProtocolError,
} from '@xepelinapp/blackops-commons-puppeteer';

import { publishBigBrother } from '!libs/bigBrother';
import {
  BrowserError,
  UploadToS3Error,
  FileNotFoundError,
  LoginError,
  GetPdfError,
  EmptyDocumentError,
  PdfParsingError,
  DocumentNotFoundError,
  UnhandledError,
  SendWebhookError,
} from '!libs/errors';
import { ExtractDataFromPdf } from '!libs/extractDataFromPdf';
import { ParsePdf } from '!libs/parsePdf';
import { UploadToS3 } from '!libs/uploadToS3';
import { sendWebhook } from '!libs/webhook';
import { GetPdfFromSat69 } from '!services/GetPdfFromSat69';
import { VALID_SIGNED_URL_TIME } from '!useCases/checkSat69/checkSat69.constants';
import { CheckSat69Dto } from '!useCases/checkSat69/checkSat69.dto';

export class CheckSat69UseCase {
  private logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  public async execute(dto: CheckSat69Dto): Promise<void> {
    const { taxID, webhook, jobId } = dto;

    await publishBigBrother({ identifier: taxID, jobId, status: 'IN_PROGRESS' });
    const expiresIn = VALID_SIGNED_URL_TIME;

    const urlSat69 = 'https://ptsc32d.clouda.sat.gob.mx/?/reporteOpinion32DContribuyente';
    const credentialsManager = new CredentialsManager(
      process.env.CREDENTIALS_MANAGER_URL as string,
      process.env.SCRAPPER_TO_CREDENTIALS_MANAGER_API_KEY as string,
      process.env.SERVICE_UUID
    );
    this.logger.debug({
      message: '----> Get Cookies to CM ...',
    });
    const cookies = (await credentialsManager.loginSat69(taxID, 10)) as unknown as Sat69Cookie[];

    this.logger.debug({
      message: '----> Received Cookies to CM ...',
    });
    const puppeteer = new Puppeteer({
      logger: this.logger,
      proxyUrl: process.env.PROXY_AGENT,
      headless: true,
    });

    await puppeteer.initBrowser();

    try {
      const page = (await puppeteer.newPage()) as unknown as Page;
      await page.setCookie(...cookies);

      // get pdf and save it
      const getPdfFromSat60 = new GetPdfFromSat69(urlSat69, this.logger);
      await getPdfFromSat60.execute(page, taxID);

      // parse pdf
      this.logger.debug({
        message: '----> Parsing pdf',
      });
      const parsePdf = new ParsePdf(this.logger);
      const parsedData = await parsePdf.execute(taxID);
      this.logger.debug({ parsedData });

      const extractDataFromPdf = new ExtractDataFromPdf();
      const extractedData = extractDataFromPdf.execute(taxID, parsedData);

      const { opinion, cadenaOriginal, date, detail, creditNumber } = extractedData;

      this.logger.debug({
        message: '----> Pdf parsed',
      });

      // upload to s3
      this.logger.debug({
        message: '----> Uploading to s3',
      });
      const uploadToS3 = new UploadToS3(this.logger);
      await uploadToS3.execute(taxID, extractedData);

      const s3 = new S3();

      const bucketName = process.env.AWS_S3_BUCKET_NAME || '';
      const path = `${taxID}.pdf`;
      const signedUrl = await s3.getS3SignedUrl({ bucketName, path, expiresIn });

      this.logger.debug({ extractedData });
      this.logger.debug({ signedUrl });

      const data = {
        date,
        chain: cadenaOriginal,
        url: signedUrl,
        creditNumber,
        taxID,
        opinion,
        detail,
      };

      await sendWebhook({ data, webhook, identifier: taxID, logger: this.logger });
      await publishBigBrother({ identifier: taxID, jobId, status: 'FINISHED' });
    } catch (err) {
      await publishBigBrother({ identifier: taxID, jobId, status: 'ERROR' });
      if (err instanceof TimeoutError || err instanceof ProtocolError) {
        this.logger.externalError(err);
        throw new BrowserError(taxID);
      } else if (
        err instanceof BrowserError ||
        err instanceof UploadToS3Error ||
        err instanceof FileNotFoundError ||
        err instanceof LoginError ||
        err instanceof GetPdfError ||
        err instanceof EmptyDocumentError ||
        err instanceof PdfParsingError ||
        err instanceof DocumentNotFoundError ||
        err instanceof SendWebhookError
      ) {
        throw err;
      }
      throw new UnhandledError(taxID);
    } finally {
      await puppeteer.close();
    }
  }
}
