import { JSONSchemaType } from 'ajv';

import { Body } from '!useCases/checkSat69/checkSat69.types';

export const schema: JSONSchemaType<Body> = {
  type: 'object',
  properties: {
    taxID: { type: 'string', nullable: false },
    jobId: { type: 'string', nullable: false },
    webhook: {
      type: 'object',
      nullable: false,
      properties: {
        url: { type: 'string', format: 'uri', nullable: false },
        headers: { type: 'object', nullable: true, required: [] },
        extras: { type: 'object', nullable: true, required: [] },
      },
      additionalProperties: false,
      required: ['url'],
    },
  },
  required: ['taxID', 'webhook'],
  additionalProperties: false,
};
