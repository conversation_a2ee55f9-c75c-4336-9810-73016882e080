import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { SQSRecord } from 'aws-lambda';

import { ValidationError } from '!libs/errors';
import { CheckSat69Dto } from '!useCases/checkSat69/checkSat69.dto';
import { schema } from '!useCases/checkSat69/checkSat69.schema';
import { Body } from '!useCases/checkSat69/checkSat69.types';

export const convertEventToDTO = (record: SQSRecord): CheckSat69Dto => {
  const body =
    typeof record.body === 'string'
      ? (JSON.parse(record.body || '{}') as Body)
      : (record.body as Body);

  const ajv = new Ajv({ allErrors: true });
  addFormats(ajv);
  const validate = ajv.compile(schema);
  if (!validate(body)) {
    throw new ValidationError();
  }

  return new CheckSat69Dto(body);
};
