import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { SQSRecord } from 'aws-lambda';

import { ValidationError } from '!libs/errors';
import { QueueRequestManagerDTO } from '!useCases/queueRequestManager/queueRequestManager.dto';
import { schema } from '!useCases/queueRequestManager/queueRequestManager.schema';
import { Body } from '!useCases/queueRequestManager/queueRequestManager.types';

export const convertEventToDTO = (record: SQSRecord): QueueRequestManagerDTO => {
  const body =
    typeof record.body === 'string'
      ? (JSON.parse(record.body || '{}') as Body)
      : (record.body as Body);

  const ajv = new Ajv({ allErrors: true });
  addFormats(ajv);
  const validate = ajv.compile(schema);
  if (!validate(body)) {
    throw new ValidationError();
  }

  return new QueueRequestManagerDTO(body);
};
