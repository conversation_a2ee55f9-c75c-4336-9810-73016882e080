import { describe, expect, it } from '@jest/globals';
import { SQSRecord } from 'aws-lambda';

import { ValidationError } from '!libs/errors';
import { convertEventToDTO } from '!useCases/queueRequestManager/queueRequestManager.mapper';

const taxIDs = ['1111111-1', '2222222-2', '3333333-3'];
const webhook = { url: 'https://wwww.test.cl' };
const mockBody = {
  taxIDs,
  webhook,
};

describe('test queueRequestManager mapper', () => {
  describe('convertEventToDTO', () => {
    it.each([mockBody, JSON.stringify(mockBody)])(
      'should successfully validate event with object body',
      (data) => {
        expect.assertions(1);

        const record = { body: data };

        const dto = convertEventToDTO(record as SQSRecord);
        const response = JSON.stringify(dto);

        expect(response).toStrictEqual(JSON.stringify({ ...mockBody }));
      }
    );

    it.each([{}, ''])('should fail to validate event', (data) => {
      expect.assertions(1);

      const record = { body: data };
      let response;

      try {
        convertEventToDTO(record as SQSRecord);
      } catch (e) {
        response = e;
      }

      expect(response).toBeInstanceOf(ValidationError);
    });
  });
});
