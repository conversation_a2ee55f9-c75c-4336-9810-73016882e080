import { describe, expect, it, jest } from '@jest/globals';
import { SQSEvent, Context } from 'aws-lambda';

import { QueueRequestManagerDTO } from '!useCases/queueRequestManager/queueRequestManager.dto';
import { queueRequestManager } from '!useCases/queueRequestManager/queueRequestManager.handler';
import * as mapper from '!useCases/queueRequestManager/queueRequestManager.mapper';
import { Body } from '!useCases/queueRequestManager/queueRequestManager.types';
import { QueueRequestManagerUseCase } from '!useCases/queueRequestManager/queueRequestManager.useCase';

const taxIDs = ['1111111-1', '2222222-2', '3333333-3'];
const webhook = { url: 'https://wwww.test.cl' };
const jobId = '1-1';
const body: Body = {
  taxIDs,
  jobId,
  webhook,
};
const mockDTO = new QueueRequestManagerDTO(body);
const mockedConvertEventToDTO = jest.spyOn(mapper, 'convertEventToDTO');

describe('test enqueue Manager handler', () => {
  const mockedExecute = jest.spyOn(QueueRequestManagerUseCase.prototype, 'execute');

  describe('test queueRequestManager handler', () => {
    it('should return successful message', async () => {
      expect.assertions(2);

      jest.clearAllMocks();

      mockedConvertEventToDTO.mockReturnValue(new QueueRequestManagerDTO(mockDTO));
      mockedExecute.mockResolvedValue();

      await queueRequestManager(
        { Records: [{ body: JSON.stringify(body) }] } as SQSEvent,
        {
          functionName: 'test',
        } as Context
      );

      expect(mockedExecute).toHaveBeenCalledTimes(1);
      expect(mockedExecute).toHaveBeenCalledWith(mockDTO);
    });

    it('should return with empty body', async () => {
      expect.assertions(2);

      jest.clearAllMocks();

      await queueRequestManager(
        { Records: [{ body: '' }] } as SQSEvent,
        {
          functionName: 'test',
        } as Context
      );

      expect(mockedExecute).toHaveBeenCalledTimes(0);
      expect(mockedConvertEventToDTO).toHaveBeenCalledTimes(0);
    });

    it('should return with empty Record', async () => {
      expect.assertions(2);

      jest.clearAllMocks();

      const mockedConvertEventToDTO = jest.spyOn(mapper, 'convertEventToDTO');
      const mockedExecute = jest.spyOn(QueueRequestManagerUseCase.prototype, 'execute');

      await queueRequestManager(
        {} as SQSEvent,
        {
          functionName: 'test',
        } as Context
      );

      expect(mockedExecute).toHaveBeenCalledTimes(0);
      expect(mockedConvertEventToDTO).toHaveBeenCalledTimes(0);
    });

    it('should return generic error', async () => {
      expect.assertions(1);

      jest.clearAllMocks();

      mockedConvertEventToDTO.mockReturnValue(new QueueRequestManagerDTO(mockDTO));
      mockedExecute.mockImplementation(() => {
        throw Error();
      });

      await expect(
        queueRequestManager(
          { Records: [{ body: JSON.stringify(body) }] } as SQSEvent,
          {
            functionName: 'test',
          } as Context
        )
      ).rejects.toThrow(new Error());
    });
  });
});
