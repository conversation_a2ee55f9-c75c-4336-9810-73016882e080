import { describe, expect, it, jest } from '@jest/globals';
import * as AWS from '@xepelinapp/blackops-commons-aws-lambda';
import * as CM from '@xepelinapp/blackops-commons-credentials-manager-lambda';

import { QueueRequestManagerDTO } from '!useCases/queueRequestManager/queueRequestManager.dto';
import { QueueRequestManagerUseCase } from '!useCases/queueRequestManager/queueRequestManager.useCase';

jest.mock<typeof AWS.SQS>('@xepelinapp/blackops-commons-aws-lambda');
jest.mock<typeof CM.CredentialsManager>(
  '@xepelinapp/blackops-commons-credentials-manager-lambda',
  () => {
    class CredentialsManager {
      getAllActiveIdentifiers() {
        return [];
      }
    }
    return {
      CredentialsManager,
    } as unknown as typeof CM.CredentialsManager;
  }
);

const url = 'https://wwww.test.cl';
const webhook = { url };
const jobId = 'aaa111';
const identifier = '11111111-1';

jest.clearAllMocks();

describe('test queueRequestManager use case', () => {
  const mockEnqueueMessage = jest.spyOn(AWS.SQS.prototype, 'enqueueMessage');
  const mockedCredentialsManager = jest.spyOn(
    CM.CredentialsManager.prototype,
    'getAllActiveIdentifiers'
  );
  const logSpy = jest.spyOn(console, 'log');

  describe('test queue request Manager', () => {
    it('should queue requested taxIDs', async () => {
      expect.assertions(2);

      const taxIDs = [identifier];
      mockEnqueueMessage.mockResolvedValue('fake-message-id');

      await new QueueRequestManagerUseCase().execute(
        new QueueRequestManagerDTO({ taxIDs, jobId, webhook })
      );

      expect(logSpy).toHaveBeenCalledWith(
        'Successfully sent to enqueue 1 identifiers from a total of 1 (0 errors)'
      );
      expect(mockEnqueueMessage).toHaveBeenCalledWith('test-queue-name', {
        taxID: identifier,
        jobId,
        webhook: { url },
      });
    });

    it('should enqueue all taxIDs', async () => {
      expect.assertions(2);

      mockEnqueueMessage.mockResolvedValue('fake-message-id');

      mockedCredentialsManager.mockResolvedValueOnce([
        {
          id: 1,
          identifier,
          businessId: 1,
          createdAt: '2023-08-04T15:47:49.452Z',
          updatedAt: '2023-08-04T15:47:49.452Z',
        },
      ]);

      await new QueueRequestManagerUseCase().execute(
        new QueueRequestManagerDTO({ taxIDs: [], jobId, webhook })
      );

      expect(logSpy).toHaveBeenCalledWith(
        'Successfully sent to enqueue 1 identifiers from a total of 1 (0 errors)'
      );
      expect(mockEnqueueMessage).toHaveBeenCalledWith('test-queue-name', {
        taxID: '11111111-1',
        jobId,
        webhook: { url },
      });
    });
  });
});
