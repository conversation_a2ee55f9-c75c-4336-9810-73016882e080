import { PromisePool } from '@supercharge/promise-pool';
import { SQS } from '@xepelinapp/blackops-commons-aws-lambda';
import { CredentialsManager } from '@xepelinapp/blackops-commons-credentials-manager-lambda';
import { createLogger } from '@xepelinapp/blackops-commons-logger';

import { publishBigBrother } from '!libs/bigBrother';
import { QueueRequestManagerDTO } from '!useCases/queueRequestManager/queueRequestManager.dto';

export class QueueRequestManagerUseCase {
  private logger;

  constructor() {
    this.logger = createLogger();
  }

  public async execute(dto: QueueRequestManagerDTO): Promise<void> {
    const { taxIDs, webhook, jobId } = dto;

    let activeIdentifiers: string[];

    if (!taxIDs.length) {
      const credentialsManager = new CredentialsManager(
        process.env.CREDENTIALS_MANAGER_URL as string,
        process.env.SCRAPPER_TO_CREDENTIALS_MANAGER_API_KEY as string,
        process.env.SERVICE_UUID
      );
      const activeIdentifiersData = await credentialsManager.getAllActiveIdentifiers();
      activeIdentifiers = activeIdentifiersData.map(
        (activeIdentifier: { identifier: string }) => activeIdentifier.identifier
      );
    } else {
      activeIdentifiers = taxIDs;
    }

    const sqsClient = new SQS();

    const { results, errors } = await PromisePool.for(activeIdentifiers)
      .withConcurrency(5)
      .process(async (identifier): Promise<{ taxID: string; MessageId: string }> => {
        const message = { taxID: identifier, webhook, jobId };
        const response = await sqsClient.enqueueMessage(
          process.env.AWS_SQS_QUEUE_URL as string,
          message
        );
        await publishBigBrother({ identifier, jobId, status: 'STARTED' });
        return { taxID: identifier, MessageId: response };
      });

    this.logger.info({
      message: `Successfully sent to enqueue ${results.length} identifiers from a total of ${activeIdentifiers.length} (${errors.length} errors)`,
    });
  }
}
