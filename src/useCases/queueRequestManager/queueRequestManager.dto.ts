import { Body } from '!useCases/queueRequestManager/queueRequestManager.types';

export class QueueRequestManagerDTO {
  public taxIDs: string[];
  jobId: string;
  public webhook: {
    url: string;
    headers?: Record<string, string>;
    extras?: Record<string, string>;
  };

  constructor(body: Body) {
    this.taxIDs = body.taxIDs;
    this.webhook = body.webhook;
    this.jobId = body.jobId;
  }
}
