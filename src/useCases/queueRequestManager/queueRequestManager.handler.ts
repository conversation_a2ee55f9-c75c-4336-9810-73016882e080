import { SQSEvent, Context } from 'aws-lambda';

import { initLogger } from '!libs/logger';
import { convertEventToDTO } from '!useCases/queueRequestManager/queueRequestManager.mapper';
import { QueueRequestManagerUseCase } from '!useCases/queueRequestManager/queueRequestManager.useCase';

export const queueRequestManager = async (event: SQSEvent, context: Context): Promise<void> => {
  const logger = initLogger(context, context.functionName);
  try {
    const { Records: records = [] } = event;
    const promises = records.map(async (record) => {
      if (!record.body) {
        return;
      }
      const dto = convertEventToDTO(record);
      const useCase = new QueueRequestManagerUseCase();
      await useCase.execute(dto);
    });
    await Promise.all(promises);
  } catch (e) {
    logger.error(e);
    throw e;
  }
};
