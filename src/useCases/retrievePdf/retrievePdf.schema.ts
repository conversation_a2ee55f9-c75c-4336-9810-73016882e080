import { JSONSchemaType } from 'ajv';

import { Data } from '!useCases/retrievePdf/retrievePdf.types';

export const schema: JSONSchemaType<Data> = {
  type: 'object',
  properties: {
    pathParameters: {
      type: 'object',
      properties: {
        taxID: { type: 'string', nullable: false },
      },
      nullable: false,
      required: ['taxID'],
      additionalProperties: false,
    },
    queryStringParameters: {
      type: 'object',
      properties: {
        fileType: { type: 'string', enum: ['pdf', 'json'], nullable: true },
      },
      nullable: true,
      additionalProperties: false,
    },
  },
  required: ['pathParameters'],
  additionalProperties: false,
};
