import { S3 } from '@xepelinapp/blackops-commons-aws-lambda';

import { FileNotFoundError } from '!libs/errors';
import {
  VALID_SIGNED_URL_TIME,
  MIN_FILESIZE_WITHOUT_ERRORS,
} from '!useCases/retrievePdf/retrievePdf.constants';
import { RetrievePdfHttpDTO, RetrievePdfResponseDTO } from '!useCases/retrievePdf/retrievePdf.dto';

export class RetrievePdfUseCase {
  public async execute(dto: RetrievePdfHttpDTO): Promise<RetrievePdfResponseDTO> {
    const { taxID, fileType } = dto;
    const path = `${taxID}.${fileType}`;
    const minSize = fileType === 'pdf' ? MIN_FILESIZE_WITHOUT_ERRORS : 0;
    const bucketName = process.env.AWS_S3_BUCKET_NAME || '';

    const s3 = new S3();
    const ifFileExists = await s3.checkFileExists({ bucketName, path, minSize });
    if (ifFileExists) {
      const url = await s3.getS3SignedUrl({ bucketName, path, expiresIn: VALID_SIGNED_URL_TIME });
      return new RetrievePdfResponseDTO(taxID, url);
    } else {
      throw new FileNotFoundError();
    }
  }
}
