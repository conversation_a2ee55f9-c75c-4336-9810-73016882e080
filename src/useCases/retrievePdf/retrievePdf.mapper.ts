import Ajv from 'ajv';
import { APIGatewayProxyEvent } from 'aws-lambda';

import { ValidationError } from '!libs/errors';
import { RetrievePdfHttpDTO } from '!useCases/retrievePdf/retrievePdf.dto';
import { schema } from '!useCases/retrievePdf/retrievePdf.schema';

export const convertEventToDTO = (event: APIGatewayProxyEvent): RetrievePdfHttpDTO => {
  const { pathParameters, queryStringParameters } = event;

  const data = { pathParameters, queryStringParameters };

  const ajv = new Ajv({ allErrors: true });
  const validate = ajv.compile(schema);

  if (!validate(data)) {
    throw new ValidationError();
  }

  return new RetrievePdfHttpDTO(
    (pathParameters as { taxID: string }).taxID,
    queryStringParameters?.fileType
  );
};
