import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';

import { ValidationError, FileNotFoundError } from '!libs/errors';
import { responseHandler } from '!libs/httpResponse';
import { initLogger } from '!libs/logger';
import { convertEventToDTO } from '!useCases/retrievePdf/retrievePdf.mapper';
import { RetrievePdfUseCase } from '!useCases/retrievePdf/retrievePdf.useCase';

export const retrievePdf = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  const logger = initLogger(context, context.functionName);

  try {
    const dto = convertEventToDTO(event);
    const useCase = new RetrievePdfUseCase();
    return responseHandler(JSON.stringify(await useCase.execute(dto)));
  } catch (e) {
    if (e instanceof ValidationError) {
      return responseHandler('Validation error', 422);
    } else if (e instanceof FileNotFoundError) {
      return responseHandler('Document not found.', 404);
    } else {
      logger.error(e);
      return responseHandler('Internal server error', 500);
    }
  }
};
