import { describe, expect, it, jest } from '@jest/globals';
import { S3 } from '@xepelinapp/blackops-commons-aws-lambda';

import { RetrievePdfHttpDTO, RetrievePdfResponseDTO } from '!useCases/retrievePdf/retrievePdf.dto';
import { RetrievePdfUseCase } from '!useCases/retrievePdf/retrievePdf.useCase';

const taxID = 'TEST-TAXID';
const fileType = 'pdf';
const url = 'test-signed-url';

describe('test invoiceDownloader.useCase', () => {
  describe('test invoiceDownloader.useCase.execute', () => {
    it('should return InvoiceDownloaderResponseDTO', async () => {
      expect.assertions(1);

      const dto = new RetrievePdfHttpDTO(taxID, fileType);

      const expectedResponse = new RetrievePdfResponseDTO(taxID, url);

      jest.spyOn(S3.prototype, 'checkFileExists').mockResolvedValue(true);
      jest.spyOn(S3.prototype, 'getS3SignedUrl').mockResolvedValue(url);

      const useCase = new RetrievePdfUseCase();
      const response = await useCase.execute(dto);

      expect(response).toStrictEqual(expectedResponse);
    });

    it('should throw File not Found', async () => {
      expect.assertions(1);

      const dto = new RetrievePdfHttpDTO(taxID, fileType);

      jest.spyOn(S3.prototype, 'checkFileExists').mockResolvedValue(false);

      const useCase = new RetrievePdfUseCase();

      await expect(useCase.execute(dto)).rejects.toThrow('File not found');
    });
  });
});
