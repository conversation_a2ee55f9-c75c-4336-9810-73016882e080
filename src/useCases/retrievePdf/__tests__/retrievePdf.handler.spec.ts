import { describe, expect, it, jest } from '@jest/globals';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { FileNotFoundError, ValidationError } from '!libs/errors';
import { retrievePdf } from '!useCases/retrievePdf/retrievePdf.handler';
import * as mapper from '!useCases/retrievePdf/retrievePdf.mapper';
import { RetrievePdfUseCase } from '!useCases/retrievePdf/retrievePdf.useCase';

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
  'X-Frame-Options': 'SAMEORIGIN',
  'X-XSS-Protection': '1',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'no-referrer',
  'Content-Security-Policy': 'no-referrer',
};

const taxID = 'test-taxID';
const fileType = 'pdf';
const url = 'test-url';

describe('test retrievePdf handler', () => {
  const callRetrievePdf = async ({
    pathParameters,
    queryStringParameters,
  }: {
    pathParameters: {
      taxID: string;
    };
    queryStringParameters: { fileType: string };
  }) => {
    return retrievePdf(
      {
        pathParameters,
        queryStringParameters,
      } as unknown as APIGatewayProxyEvent,
      {
        functionName: 'test',
      } as Context
    );
  };

  describe('test response', () => {
    it('should return successful response', async () => {
      expect.assertions(2);

      const mockedDto = jest.spyOn(mapper, 'convertEventToDTO');
      mockedDto.mockReturnValue({ taxID, fileType });

      const mockedUseCase = jest.spyOn(RetrievePdfUseCase.prototype, 'execute');
      mockedUseCase.mockResolvedValue({ taxID, url });

      const response = await callRetrievePdf({
        pathParameters: { taxID },
        queryStringParameters: { fileType },
      });

      expect(response).toStrictEqual({
        headers,
        statusCode: 200,
        body: `{"taxID":"${taxID}","url":"${url}"}`,
      });
      expect(mockedUseCase).toHaveBeenCalledWith({ taxID, fileType });
    });

    it('should return validation Error', async () => {
      expect.assertions(1);

      const mockedDto = jest.spyOn(mapper, 'convertEventToDTO');
      mockedDto.mockImplementation(() => {
        throw new ValidationError();
      });

      const response = await callRetrievePdf({
        pathParameters: null,
        queryStringParameters: null,
      } as unknown as never);

      expect(response).toMatchObject({
        headers,
        statusCode: 422,
        body: `Validation error`,
      });
    });

    it('should return FileNotFoundError', async () => {
      expect.assertions(1);

      jest.spyOn(console, 'error').mockImplementation(() => 'Error');

      const mockedDto = jest.spyOn(mapper, 'convertEventToDTO');
      mockedDto.mockReturnValue({ taxID, fileType });

      const mockedUseCase = jest.spyOn(RetrievePdfUseCase.prototype, 'execute');
      mockedUseCase.mockImplementation(() => {
        throw new FileNotFoundError();
      });

      const response = await callRetrievePdf({
        pathParameters: { taxID },
        queryStringParameters: { fileType },
      });

      expect(response).toMatchObject({
        headers,
        statusCode: 404,
        body: `Document not found.`,
      });
    });

    it('should return generic error', async () => {
      expect.assertions(1);

      jest.spyOn(console, 'error').mockImplementation(() => 'Error');

      const mockedDto = jest.spyOn(mapper, 'convertEventToDTO');
      mockedDto.mockReturnValue({ taxID, fileType });

      const mockedUseCase = jest.spyOn(RetrievePdfUseCase.prototype, 'execute');
      mockedUseCase.mockImplementation(() => {
        throw Error();
      });

      const response = await callRetrievePdf({
        pathParameters: JSON.stringify({ taxID }),
        queryStringParameters: { fileType },
      } as unknown as never);

      expect(response).toMatchObject({
        headers,
        statusCode: 500,
        body: `Internal server error`,
      });
    });
  });
});
