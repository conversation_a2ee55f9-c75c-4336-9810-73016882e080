import { describe, expect, it } from '@jest/globals';
import { APIGatewayProxyEvent } from 'aws-lambda';

import { ValidationError } from '!libs/errors';
import { convertEventToDTO } from '!useCases/retrievePdf/retrievePdf.mapper';

const taxID = 'test-taxID';

describe('test invoiceDownloader mapper', () => {
  describe('convertEventToDTO', () => {
    it('should successfully validate event with object body without filetype', () => {
      expect.assertions(1);

      const event = { pathParameters: { taxID } };

      const dto = convertEventToDTO(event as unknown as APIGatewayProxyEvent);
      const response = JSON.stringify(dto);

      expect(response).toBe('{"taxID":"TEST-TAXID","fileType":"pdf"}');
    });

    it.each([
      {
        event: {
          pathParameters: { taxID },
          queryStringParameters: { fileType: 'pdf' },
        },
        fileType: 'pdf',
      },
      {
        event: {
          pathParameters: { taxID },
          queryStringParameters: { fileType: 'json' },
        },
        fileType: 'json',
      },
    ])('should successfully validate event with object body with fileType', (input) => {
      expect.assertions(1);

      const dto = convertEventToDTO(input.event as unknown as APIGatewayProxyEvent);
      const response = JSON.stringify(dto);

      expect(response).toBe(`{"taxID":"TEST-TAXID","fileType":"${input.fileType}"}`);
    });

    it.each([{}, ''])('should fail to validate event', (event) => {
      expect.assertions(1);

      let response;

      try {
        convertEventToDTO(event as unknown as APIGatewayProxyEvent);
      } catch (e) {
        response = e;
      }

      expect(response).toBeInstanceOf(ValidationError);
    });
  });
});
