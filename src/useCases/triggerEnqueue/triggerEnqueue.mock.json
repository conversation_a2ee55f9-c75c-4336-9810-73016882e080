{"version": "2.0", "routeKey": "POST /triggerEnqueue", "rawPath": "/triggerEnqueue", "rawQueryString": "", "headers": {"accept": "*/*", "accept-encoding": "gzip, deflate, br", "cache-control": "no-cache", "content-length": "44", "content-type": "application/json", "host": "1mwhz0gr12.execute-api.us-east-1.amazonaws.com", "postman-token": "2b90c141-4bc7-42bd-bd7a-52aa4b1545a0", "user-agent": "PostmanRuntime/7.30.0", "x-amzn-trace-id": "Root=1-63ea2d59-72e34f2f5e5496695408dc06", "x-forwarded-for": "************", "x-forwarded-port": "443", "x-forwarded-proto": "https"}, "requestContext": {"accountId": "************", "apiId": "1mwhz0gr12", "domainName": "1mwhz0gr12.execute-api.us-east-1.amazonaws.com", "domainPrefix": "1mwhz0gr12", "http": {"method": "POST", "path": "/triggerEnqueue", "protocol": "HTTP/1.1", "sourceIp": "************", "userAgent": "PostmanRuntime/7.30.0"}, "requestId": "ARwGAgJBoAMEcXw=", "routeKey": "POST /triggerEnqueue", "stage": "$default", "time": "13/Feb/2023:12:30:17 +0000", "timeEpoch": *************}, "body": "{\n    \"users\": [\n        \"57653\"\n    ]\n}", "isBase64Encoded": false}