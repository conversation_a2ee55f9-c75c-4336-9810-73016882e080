import { Body } from '!useCases/triggerEnqueue/triggerEnqueue.types';

export class TriggerEnqueueHttpDTO {
  public taxIDs: string[];
  public webhook: {
    url: string;
    headers?: Record<string, string>;
    extras?: Record<string, string>;
  };

  constructor(body: Body) {
    this.taxIDs = body.taxIDs;
    this.webhook = body.webhook;
  }
}

export class TriggerEnqueueResponseDTO {
  public message: string;

  constructor(message: string) {
    this.message = message;
  }
}
