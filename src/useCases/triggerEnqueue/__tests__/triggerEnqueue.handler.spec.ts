import { describe, expect, it, jest } from '@jest/globals';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { ValidationError, InvalidIdentifiersError } from '!libs/errors';
import {
  TriggerEnqueueResponseDTO,
  TriggerEnqueueHttpDTO,
} from '!useCases/triggerEnqueue/triggerEnqueue.dto';
import { triggerEnqueue } from '!useCases/triggerEnqueue/triggerEnqueue.handler';
import * as mapper from '!useCases/triggerEnqueue/triggerEnqueue.mapper';
import { TriggerEnqueueUseCase } from '!useCases/triggerEnqueue/triggerEnqueue.useCase';

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
  'X-Frame-Options': 'SAMEORIGIN',
  'X-XSS-Protection': '1',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'no-referrer',
  'Content-Security-Policy': 'no-referrer',
};

const taxIDs = ['1111111-1', '2222222-2', '3333333-3'];
const url = 'https://wwww.test.cl';
const webhook = { url };
const mockBody = {
  taxIDs,
  webhook,
};

describe('test triggerEnqueue handler', () => {
  const name = 'test-enqueue';

  const callTriggerEnqueueAuthorizer = async (bodyContent: string | null) => {
    return triggerEnqueue(
      {
        body: bodyContent,
      } as APIGatewayProxyEvent,
      {
        functionName: name,
      } as Context
    );
  };

  const mockConvertEventToDTO = jest.spyOn(mapper, 'convertEventToDTO');
  const mockTriggerEnqueueUseCase = jest.spyOn(TriggerEnqueueUseCase.prototype, 'execute');
  const mockDTO = new TriggerEnqueueHttpDTO(mockBody);

  describe('test handler', () => {
    it('should call triggerEnqueue', async () => {
      expect.assertions(3);

      const mockSummaryMessage =
        'Successfully sent to enqueue 1 identifiers from a total of 1 (0 errors)';

      mockConvertEventToDTO.mockReturnValue(mockDTO);

      mockTriggerEnqueueUseCase.mockResolvedValue(
        new TriggerEnqueueResponseDTO(mockSummaryMessage)
      );

      const response = await callTriggerEnqueueAuthorizer(JSON.stringify(mockBody));

      expect(response).toStrictEqual({
        headers,
        statusCode: 200,
        body: JSON.stringify({ message: mockSummaryMessage }),
      });
      expect(mockConvertEventToDTO).toHaveBeenCalledWith({ body: JSON.stringify(mockBody) });
      expect(mockTriggerEnqueueUseCase).toHaveBeenCalledWith(mockDTO);
    });

    it('should throw validation error', async () => {
      expect.assertions(2);

      jest.resetAllMocks();

      mockConvertEventToDTO.mockImplementation(() => {
        throw new ValidationError();
      });

      const response = await callTriggerEnqueueAuthorizer(JSON.stringify(mockBody));

      expect(response).toStrictEqual({
        headers,
        statusCode: 422,
        body: 'Validation error',
      });
      expect(mockTriggerEnqueueUseCase).toHaveBeenCalledTimes(0);
    });

    it('should throw InvalidIdentifiersError', async () => {
      expect.assertions(1);

      jest.resetAllMocks();

      mockConvertEventToDTO.mockReturnValue(mockDTO);

      mockTriggerEnqueueUseCase.mockImplementation(() => {
        throw new InvalidIdentifiersError();
      });

      const response = await callTriggerEnqueueAuthorizer(JSON.stringify(mockBody));

      expect(response).toStrictEqual({
        headers,
        statusCode: 422,
        body: 'Validation error: All identifiers were inactive or did not exist',
      });
    });

    it('should throw generic Error', async () => {
      expect.assertions(1);

      jest.resetAllMocks();

      mockConvertEventToDTO.mockReturnValue(mockDTO);

      mockTriggerEnqueueUseCase.mockImplementation(() => {
        throw new Error();
      });

      const response = await callTriggerEnqueueAuthorizer(JSON.stringify(mockBody));

      expect(response).toStrictEqual({
        headers,
        statusCode: 500,
        body: 'Internal server error',
      });
    });
  });
});
