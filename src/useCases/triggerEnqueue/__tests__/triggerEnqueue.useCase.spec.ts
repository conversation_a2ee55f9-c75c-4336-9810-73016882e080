import { describe, expect, it, jest } from '@jest/globals';
import * as AWS from '@xepelinapp/blackops-commons-aws-lambda';
import * as CM from '@xepelinapp/blackops-commons-credentials-manager-lambda';
import * as Logger from '@xepelinapp/blackops-commons-logger';

import { InvalidIdentifiersError } from '!libs/errors';
import { TriggerEnqueueHttpDTO } from '!useCases/triggerEnqueue/triggerEnqueue.dto';
import { TriggerEnqueueUseCase } from '!useCases/triggerEnqueue/triggerEnqueue.useCase';

jest.mock<typeof AWS.SQS>('@xepelinapp/blackops-commons-aws-lambda');
jest.mock<typeof CM.CredentialsManager>(
  '@xepelinapp/blackops-commons-credentials-manager-lambda',
  () => {
    class CredentialsManager {
      filterInactiveIdentifiers() {
        return [];
      }
      getAllActiveIdentifiers() {
        return [];
      }
    }
    return {
      CredentialsManager,
    } as unknown as typeof CM.CredentialsManager;
  }
);

const url = 'https://wwww.test.cl';
const webhook = { url };
const identifier = '11111111-1';
const invalidIdentifier = 'invalid-identifier';
const createdAt = '2023-08-04T15:47:49.452Z';
const updatedAt = '2023-08-04T15:47:49.452Z';
const mockMessageId = 'fake-message-id';

jest.clearAllMocks();

describe('test queueRequestManager use case', () => {
  const mockEnqueueMessage = jest.spyOn(AWS.SQS.prototype, 'enqueueMessage');
  const mockedFilterInactiveIdentifiers = jest.spyOn(
    CM.CredentialsManager.prototype,
    'filterInactiveIdentifiers'
  );
  const mockedGetAllActiveIdentifiers = jest.spyOn(
    CM.CredentialsManager.prototype,
    'getAllActiveIdentifiers'
  );
  const logger = Logger.createLogger({ name: 'testLogger', serviceId: 'testService' });

  const mockedLogger = jest.spyOn(logger, 'debug');

  describe('test triggerEnqueue', () => {
    it('should queue requested taxIDs', async () => {
      expect.assertions(2);

      const taxIDs = [identifier, invalidIdentifier];
      mockEnqueueMessage.mockResolvedValue(mockMessageId);
      mockedFilterInactiveIdentifiers.mockResolvedValueOnce({
        validIdentifiers: [
          {
            id: 1,
            identifier,
            businessId: 1,
            createdAt,
            updatedAt,
          },
        ],
        invalidIdentifiers: [invalidIdentifier],
      });

      await new TriggerEnqueueUseCase().execute(new TriggerEnqueueHttpDTO({ taxIDs, webhook }));

      expect(mockedLogger).toHaveBeenCalledWith(
        'Successfully sent to enqueue 1 identifiers from a total of 2 (1 errors)'
      );
      expect(mockEnqueueMessage).toHaveBeenCalledWith('test-queue-name', {
        taxIDs: [identifier],
        webhook: { url },
      });
    });

    it('should throw InvalidIdentifiersError', async () => {
      expect.assertions(1);

      const taxIDs = [invalidIdentifier];
      mockEnqueueMessage.mockResolvedValue(mockMessageId);
      mockedFilterInactiveIdentifiers.mockResolvedValueOnce({
        validIdentifiers: [],
        invalidIdentifiers: [invalidIdentifier],
      });

      await expect(
        new TriggerEnqueueUseCase().execute(new TriggerEnqueueHttpDTO({ taxIDs, webhook }))
      ).rejects.toThrow(InvalidIdentifiersError);
    });

    it('should queue all taxIDs', async () => {
      expect.assertions(2);

      mockEnqueueMessage.mockResolvedValue(mockMessageId);
      mockedGetAllActiveIdentifiers.mockResolvedValueOnce([
        {
          id: 1,
          identifier,
          businessId: 1,
          createdAt,
          updatedAt,
        },
      ]);

      await new TriggerEnqueueUseCase().execute(new TriggerEnqueueHttpDTO({ taxIDs: [], webhook }));

      expect(mockedLogger).toHaveBeenCalledWith(
        'Successfully sent to enqueue 1 identifiers from a total of 1 (0 errors)'
      );
      expect(mockEnqueueMessage).toHaveBeenCalledWith('test-queue-name', {
        taxIDs: [],
        webhook: { url },
      });
    });
  });
});
