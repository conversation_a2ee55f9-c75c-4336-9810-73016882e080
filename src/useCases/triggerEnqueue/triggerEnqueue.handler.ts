import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';

import { ValidationError, InvalidIdentifiersError } from '!libs/errors';
import { responseHandler } from '!libs/httpResponse';
import { initLogger } from '!libs/logger';
import { convertEventToDTO } from '!useCases/triggerEnqueue/triggerEnqueue.mapper';
import { TriggerEnqueueUseCase } from '!useCases/triggerEnqueue/triggerEnqueue.useCase';

export const triggerEnqueue = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  const logger = initLogger(context, context.functionName);
  try {
    const dto = convertEventToDTO(event);
    const useCase = new TriggerEnqueueUseCase();
    const response = await useCase.execute(dto);
    return responseHandler(JSON.stringify(response));
  } catch (e) {
    if (e instanceof ValidationError) {
      return responseHandler('Validation error', 422);
    } else if (e instanceof InvalidIdentifiersError) {
      return responseHandler(
        'Validation error: All identifiers were inactive or did not exist',
        422
      );
    }
    logger.error(e);
    return responseHandler('Internal server error', 500);
  }
};
