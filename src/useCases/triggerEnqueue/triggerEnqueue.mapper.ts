import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { APIGatewayProxyEvent } from 'aws-lambda';

import { ValidationError } from '!libs/errors';
import { TriggerEnqueueHttpDTO } from '!useCases/triggerEnqueue/triggerEnqueue.dto';
import { schema } from '!useCases/triggerEnqueue/triggerEnqueue.schema';
import { Body } from '!useCases/triggerEnqueue/triggerEnqueue.types';

export const convertEventToDTO = (event: APIGatewayProxyEvent): TriggerEnqueueHttpDTO => {
  const body =
    typeof event.body === 'string'
      ? (JSON.parse(event.body || '{}') as Body)
      : (event.body as unknown as Body);

  const ajv = new Ajv({ allErrors: true });
  addFormats(ajv);
  const validate = ajv.compile(schema);

  if (!validate(body)) {
    throw new ValidationError();
  }

  return new TriggerEnqueueHttpDTO(body);
};
