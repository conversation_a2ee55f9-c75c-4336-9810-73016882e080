import { randomUUID } from 'crypto';

import { SQS } from '@xepelinapp/blackops-commons-aws-lambda';
import { CredentialsManager } from '@xepelinapp/blackops-commons-credentials-manager-lambda';
import { createLogger } from '@xepelinapp/blackops-commons-logger';

import { InvalidIdentifiersError } from '!libs/errors';
import {
  TriggerEnqueueHttpDTO,
  TriggerEnqueueResponseDTO,
} from '!useCases/triggerEnqueue/triggerEnqueue.dto';

export class TriggerEnqueueUseCase {
  private logger;

  constructor() {
    this.logger = createLogger();
  }

  public async execute(dto: TriggerEnqueueHttpDTO): Promise<TriggerEnqueueResponseDTO> {
    let taxIDs = dto.taxIDs;
    const webhook = dto.webhook;
    const jobId = randomUUID();

    const credentialsManager = new CredentialsManager(
      process.env.CREDENTIALS_MANAGER_URL as string,
      process.env.SCRAPPER_TO_CREDENTIALS_MANAGER_API_KEY as string,
      process.env.SERVICE_UUID
    );

    let identifiersAmount = 0;
    let invalidIdentifiersAmount = 0;

    if (taxIDs.length) {
      const { validIdentifiers, invalidIdentifiers } =
        await credentialsManager.filterInactiveIdentifiers(taxIDs);

      taxIDs = validIdentifiers.map(
        (validIdentifier: { identifier: string }) => validIdentifier.identifier
      );
      identifiersAmount = validIdentifiers.length;
      invalidIdentifiersAmount = invalidIdentifiers.length;
    } else {
      const validIdentifiers = await credentialsManager.getAllActiveIdentifiers();
      identifiersAmount = validIdentifiers.length;
    }

    if (!identifiersAmount) {
      throw new InvalidIdentifiersError();
    }

    const sqsClient = new SQS();

    await sqsClient.enqueueMessage(process.env.AWS_SQS_REQUEST_QUEUE_URL as string, {
      taxIDs,
      jobId,
      webhook,
    });

    const summaryMessage = `Successfully sent to enqueue ${identifiersAmount} identifiers from a total of ${
      identifiersAmount + invalidIdentifiersAmount
    } (${invalidIdentifiersAmount} errors). JobId: ${jobId}`;
    this.logger.info({
      message: summaryMessage,
    });
    return new TriggerEnqueueResponseDTO(summaryMessage);
  }
}
