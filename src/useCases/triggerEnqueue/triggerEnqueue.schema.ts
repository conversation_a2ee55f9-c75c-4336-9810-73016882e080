import { JSONSchemaType } from 'ajv';

import { Body } from '!useCases/triggerEnqueue/triggerEnqueue.types';

export const schema: JSONSchemaType<Body> = {
  type: 'object',
  properties: {
    taxIDs: { type: 'array', items: { type: 'string' }, nullable: false },
    webhook: {
      type: 'object',
      nullable: false,
      properties: {
        url: { type: 'string', format: 'uri', nullable: false },
        headers: { type: 'object', nullable: true, required: [] },
        extras: { type: 'object', nullable: true, required: [] },
      },
      additionalProperties: false,
      required: ['url'],
    },
  },
  required: ['taxIDs', 'webhook'],
  additionalProperties: false,
};
