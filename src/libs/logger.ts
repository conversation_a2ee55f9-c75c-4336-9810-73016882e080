import { LogFormat, LogLevel, createLogger } from '@xepelinapp/blackops-commons-logger';
import { Context } from 'aws-lambda';

export const initLogger = (context: Context, name: string) => {
  const {
    NODE_ENV: nodeEnv,
    SERVICE_UUID: serviceUUid,
    LOG_FORMAT: logFormat = 'json',
    LOG_LEVEL: logLevel = 'info',
  } = process.env;

  return createLogger({
    name,
    format: logFormat as LogFormat,
    serviceId: serviceUUid as string,
    level: logLevel as LogLevel,
    silent: nodeEnv === 'test',
    requestId: context.awsRequestId,
  });
};
