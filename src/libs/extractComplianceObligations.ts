interface ObligationPeriod {
  obligation: string;
  periods: string[];
}

export class ComplianceObligationsExtractor {
  private extractObligationsPeriods(text: string[]): string[] {
    const obligationsData: string[] = [];

    const year = text[0].match(/\d{4}/)?.[0];
    const periods = text[1].replace(/\s+/g, ' ').split('|');

    periods.forEach((month) => {
      obligationsData.push(`${month.trim()} / ${year}`);
    });

    return obligationsData;
  }

  private extractObligationsYears(text: string): string[] {
    const years = text.replace(/\s+/g, '').split('|');

    return years.map((year) => `Anual / ${year}`);
  }

  private mergeObligationsPeriods(obligationPeriods: ObligationPeriod[]): ObligationPeriod[] {
    return obligationPeriods.reduce<ObligationPeriod[]>((merged, current) => {
      merged.push(current);

      return merged;
    }, []);
  }

  private extractComplianceText(text: string): string | null {
    const compliancePattern =
      /obligaciones que tiene registradas:\n(.*?)\nInformación importante\n/s;
    const complianceMatch = text.match(compliancePattern);
    return complianceMatch ? complianceMatch[1] : null;
  }

  private getEmptyObligation(): ObligationPeriod {
    return { obligation: '', periods: [] };
  }

  private isDocumentEnd(line: string): boolean {
    return line.includes('Cadena Original');
  }

  private getFirstWord(line: string): string {
    return line.split(' ')[0];
  }

  private isYear(str: string): boolean {
    return str.match(/\d{4}/) !== null;
  }

  private isEjercicio(str: string): boolean {
    return str.includes('Ejercicio');
  }

  private processPage(page: string): ObligationPeriod[] {
    const obligations: ObligationPeriod[] = [];
    const lines = page.split(/\n/);

    let obligation = this.getEmptyObligation();

    for (let i = 1; i < lines.length; i++) {
      if (this.isDocumentEnd(lines[i - 1])) {
        break;
      }

      let firstWord = this.getFirstWord(lines[i]);

      if (this.isYear(firstWord)) {
        obligation.obligation = lines[i - 1];
        obligation.periods = this.extractObligationsYears(lines[i]);
        obligations.push(obligation);
        obligation = this.getEmptyObligation();
        continue;
      }

      if (this.isEjercicio(firstWord)) {
        obligation.obligation = lines[i - 1];

        while (this.isEjercicio(firstWord)) {
          obligation.periods = obligation.periods.concat(
            this.extractObligationsPeriods(lines.slice(i, i + 2))
          );
          i += 2;

          if (i >= lines.length) {
            break;
          }

          firstWord = this.getFirstWord(lines[i]);
        }

        obligations.push(obligation);
        obligation = this.getEmptyObligation();
      }
    }

    return obligations;
  }

  public extract(text: string): ObligationPeriod[] {
    const complianceText = this.extractComplianceText(text);
    if (!complianceText) {
      return [];
    }

    const textPages = complianceText.split(
      'Cumplimiento de obligaciones\nSe detectan omisiones  en la presentación de las siguientes obligaciones que tiene registradas:\n'
    );
    const complianceObligations = textPages.flatMap((page) => this.processPage(page));

    return this.mergeObligationsPeriods(complianceObligations);
  }
}
