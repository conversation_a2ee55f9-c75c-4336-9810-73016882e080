import { BigBrother } from '@xepelinapp/blackops-big-brother-lib';
import { Environments, Status } from '@xepelinapp/blackops-big-brother-lib/dist/lib.types';
import { SNS } from '@xepelinapp/blackops-commons-aws-lambda';

const snsClient = new SNS();
const bigBrother = new BigBrother({
  country: 'MX',
  environment: process.env.NODE_ENV as Environments,
  publishFunction: snsClient.publish.bind(snsClient),
  silent: false,
});

export const publishBigBrother = async ({
  identifier,
  jobId,
  status,
}: {
  identifier: string;
  jobId: string;
  status: Status;
}) => {
  await bigBrother.publishMessage({
    clientIdentifier: identifier,
    jobId,
    service: 'SAT69',
    status,
    step: { name: 'scraper', status },
  });
};
