import { ComplianceObligationsExtractor } from './extractComplianceObligations';

import { PdfData } from '!types/pdfData';

export class ExtractDataFromPdf {
  public execute(rfc: string, pdfText: string): PdfData {
    const result = pdfText.match(/POSITIVO|NEGATIVO/)?.[0] || '';

    const cadenaOriginalMatch = pdfText.match(
      /\|\|[A-Z0-9-]+\|[A-Z0-9-]+\|[A-Z0-9-]+\|[A-Z]+\|\|[A-Z0-9]+\|\|/
    );
    const cadenaOriginal = cadenaOriginalMatch ? cadenaOriginalMatch[0] : '';

    let creditNumber = '';
    let detail = {};

    if (result === 'NEGATIVO') {
      const creditNumberMatch = pdfText.match(
        /Se ubican los siguientes créditos fiscales firmes o no garantizados a su cargo:(.*)\n(.*)/
      );
      creditNumber = creditNumberMatch ? creditNumberMatch[2].trim() : '';
      const complianceObligations = new ComplianceObligationsExtractor().extract(pdfText);
      detail = { complianceObligations };
    }

    const formattedDate = new Date().toLocaleDateString('en-US', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });

    return {
      opinion: result,
      cadenaOriginal: cadenaOriginal,
      creditNumber: creditNumber,
      rfc,
      date: formattedDate,
      detail,
    };
  }
}
