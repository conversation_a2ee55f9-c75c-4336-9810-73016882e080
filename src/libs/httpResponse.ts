import { APIGatewayProxyResult } from 'aws-lambda';

export const responseHandler = (
  message: string,
  statusCode = 200
): Promise<APIGatewayProxyResult> => {
  return Promise.resolve({
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'X-Frame-Options': 'SAMEORIGIN',
      'X-XSS-Protection': '1',
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'no-referrer',
      'Content-Security-Policy': 'no-referrer',
    },
    statusCode,
    body: message,
  });
};
