export class ValidationError extends <PERSON><PERSON>r {}

export class InvalidIdentifiersError extends <PERSON>rror {}

export class FileNotFoundError extends Error {
  constructor() {
    super('File not found');
  }
}

export class LoginError extends Error {
  constructor(identifier: string) {
    super(`<PERSON><PERSON> failed for user: ${identifier}`);
  }
}
export class GetPdfError extends Error {
  constructor(identifier: string) {
    super(`Get pdf failed for user: ${identifier}`);
  }
}
export class EmptyDocumentError extends Error {
  constructor(identifier: string) {
    super(`Empty document for user: ${identifier}`);
  }
}
export class PdfParsingError extends Error {
  constructor(identifier: string) {
    super(`Pdf parsing failed for user: ${identifier}`);
  }
}
export class DocumentNotFoundError extends Error {
  constructor(identifier: string) {
    super(`Document not found for user: ${identifier}`);
  }
}
export class UploadToS3Error extends <PERSON>rror {
  constructor(identifier: string) {
    super(`Upload to S3 failed for user: ${identifier}`);
  }
}

export class BrowserError extends Error {
  constructor(identifier: string) {
    super(`Navigation timeout exceeded for user: ${identifier}`);
  }
}

export class SendWebhookError extends Error {
  constructor(identifier: string) {
    super(`Sending webhook failed for user: ${identifier}`);
  }
}
export class UnhandledError extends Error {
  constructor(identifier: string) {
    super(`Unhandled error for user: ${identifier}`);
  }
}

export class RetrieveCookiesError extends Error {
  constructor(identifier: string) {
    super(`Could not retrieve cookies for user: ${identifier}`);
  }
}
