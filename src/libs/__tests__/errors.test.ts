import { describe, expect, it } from '@jest/globals';

import {
  ValidationError,
  InvalidIdentifiersError,
  FileNotFoundError,
  LoginError,
  GetPdfError,
  EmptyDocumentError,
  PdfParsingError,
  DocumentNotFoundError,
  UploadToS3Error,
  BrowserError,
  SendWebhookError,
  UnhandledError,
  RetrieveCookiesError,
} from '../errors';

describe('custom Errors', () => {
  const testCases = [
    { errorClass: ValidationError, message: '', args: [] },
    { errorClass: InvalidIdentifiersError, message: '', args: [] },
    { errorClass: FileNotFoundError, message: 'File not found', args: [] },
    { errorClass: LoginError, message: 'Login failed for user: testUser', args: ['testUser'] },
    { errorClass: GetPdfError, message: 'Get pdf failed for user: testUser', args: ['testUser'] },
    {
      errorClass: EmptyDocumentError,
      message: 'Empty document for user: testUser',
      args: ['testUser'],
    },
    {
      errorClass: PdfParsingError,
      message: 'Pdf parsing failed for user: testUser',
      args: ['testUser'],
    },
    {
      errorClass: DocumentNotFoundError,
      message: 'Document not found for user: testUser',
      args: ['testUser'],
    },
    {
      errorClass: UploadToS3Error,
      message: 'Upload to S3 failed for user: testUser',
      args: ['testUser'],
    },
    {
      errorClass: BrowserError,
      message: 'Navigation timeout exceeded for user: testUser',
      args: ['testUser'],
    },
    {
      errorClass: SendWebhookError,
      message: 'Sending webhook failed for user: testUser',
      args: ['testUser'],
    },
    {
      errorClass: UnhandledError,
      message: 'Unhandled error for user: testUser',
      args: ['testUser'],
    },
    {
      errorClass: RetrieveCookiesError,
      message: 'Could not retrieve cookies for user: testUser',
      args: ['testUser'],
    },
  ];

  it.each(testCases)(
    'should create $errorClass.name with correct message',
    ({ errorClass, message, args }) => {
      expect.hasAssertions();

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
      const errorInstance = new (Function.prototype.bind.apply(errorClass, [null, ...args]))();

      expect(errorInstance).toBeInstanceOf(Error);
      expect(errorInstance).toBeInstanceOf(errorClass);
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      expect(errorInstance.message).toBe(message);
    }
  );
});
