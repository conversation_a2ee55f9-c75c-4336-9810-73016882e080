import { describe, expect, it } from '@jest/globals';

import {
  pdfText1Page,
  pdfText2Page,
  pdfTextBimestral,
  pdfTextTrimestral,
  pdfTextAnnualObligations,
  pdfTextMonthlyBimestralAnualObligations,
  pdfToTextCaseNotFound2Semester,
  pdfTextFirstCompleteDocument,
} from './mockNegativeComplianceObligations';

import { ExtractDataFromPdf } from '!libs/extractDataFromPdf';

const JANUARY_2022 = 'Enero / 2022';
const FEBRUARY_2022 = 'Febrero / 2022';
const MARCH_2022 = 'Marzo / 2022';
const APRIL_2022 = 'Abril / 2022';
const MAY_2022 = 'Mayo / 2022';
const JUNE_2022 = 'Junio / 2022';
const JULY_2022 = 'Julio / 2022';
const AUGUST_2022 = 'Agosto / 2022';
const SEPTEMBER_2022 = 'Septiembre / 2022';
const OCTOBER_2022 = 'Octubre / 2022';
const NOVEMBER_2022 = 'Noviembre / 2022';
const DECEMBER_2022 = 'Diciembre / 2022';

const JANUARY_2023 = 'Enero / 2023';
const FEBRUARY_2023 = 'Febrero / 2023';
const MARCH_2023 = 'Marzo / 2023';
const APRIL_2023 = 'Abril / 2023';
const MAY_2023 = 'Mayo / 2023';
const JUNE_2023 = 'Junio / 2023';
const JULY_2023 = 'Julio / 2023';
const AUGUST_2023 = 'Agosto / 2023';
const SEPTEMBER_2023 = 'Septiembre / 2023';
const OCTOBER_2023 = 'Octubre / 2023';
const NOVEMBER_2023 = 'Noviembre / 2023';
const DECEMBER_2023 = 'Diciembre / 2023';

const JANUARY_2024 = 'Enero / 2024';
const FEBRUARY_2024 = 'Febrero / 2024';
const MARCH_2024 = 'Marzo / 2024';
const APRIL_2024 = 'Abril / 2024';
const MAY_2024 = 'Mayo / 2024';
const JUNE_2024 = 'Junio / 2024';
const JULY_2024 = 'Julio / 2024';
const AUGUST_2024 = 'Agosto / 2024';
const SEPTEMBER_2024 = 'Septiembre / 2024';
const OCTOBER_2024 = 'Octubre / 2024';
const NOVEMBER_2024 = 'Noviembre / 2024';
const DECEMBER_2024 = 'Diciembre / 2024';

const JANUARY_2025 = 'Enero / 2025';
const FEBRUARY_2025 = 'Febrero / 2025';
const MARCH_2025 = 'Marzo / 2025';

const SEMESTER_1_2020 = '1er Semestre / 2020';
const SEMESTER_2_2020 = '2do Semestre / 2020';

const BIMESTER_1_2021 = '1er Bimestre / 2021';
const BIMESTER_2_2021 = '2do Bimestre / 2021';
const BIMESTER_3_2021 = '3er Bimestre / 2021';
const BIMESTER_4_2021 = '4to Bimestre / 2021';
const BIMESTER_5_2021 = '5to Bimestre / 2021';
const BIMESTER_6_2021 = '6to Bimestre / 2021';

const TRIMESTER_1_2025 = '1er Trimestre / 2025';

const ANNUAL_2022 = 'Anual / 2022';
const ANNUAL_2023 = 'Anual / 2023';
const ANNUAL_2024 = 'Anual / 2024';

const TOOJ8912234NA_ORIGINAL_STRING =
  '||TOOJ8912234NA|25NC8786618|03-06-2025|N||00001088888800000031||';
const AER150304CR0_ORIGINAL_STRING =
  '||AER150304CR0|25NC8313624|01-06-2025|N||00001088888800000031||';

const PAGO_DEFINITIVO_MENSUAL_DE_IVA = 'Pago definitivo mensual de IVA.';
const DECLARACION_DE_PROVEEDORES_DE_IVA = 'Declaración de proveedores de IVA';
const ENTERO_DE_RETENCIONES_MENSUALES_DE_ISR =
  'Entero de retenciones mensuales de ISR por sueldos y salarios';

describe('extractComplianceObligations', () => {
  it('should return the expected data when given a pdf Text One Page', () => {
    expect.hasAssertions();

    const rfc = 'RFC9932124SY6';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: '||AAD150323GA3|25NC8433304|02-06-2025|N||00001088888800000031||',
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [
          {
            obligation: ENTERO_DE_RETENCIONES_MENSUALES_DE_ISR,
            periods: [JANUARY_2025, FEBRUARY_2025, MARCH_2025],
          },
        ],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();
    const result = extractDataFromPdf.execute(rfc, pdfText1Page);

    expect(result).toStrictEqual(expectedData);
  });

  it('should return the expected data when given a pdf Text Two Pages', () => {
    expect.hasAssertions();

    const rfc = 'RFCJ830810AZ2';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: AER150304CR0_ORIGINAL_STRING,
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [
          {
            obligation: ENTERO_DE_RETENCIONES_MENSUALES_DE_ISR,
            periods: [
              OCTOBER_2022,
              NOVEMBER_2022,
              DECEMBER_2022,
              JANUARY_2025,
              FEBRUARY_2025,
              MARCH_2025,
            ],
          },
          {
            obligation: 'Entero de retenciones mensuales de ISR por ingresos asimilados a salarios',
            periods: [OCTOBER_2022, NOVEMBER_2022, DECEMBER_2022],
          },
          {
            obligation: 'Pago provisional mensual de ISR personas morales régimen general',
            periods: [
              OCTOBER_2022,
              NOVEMBER_2022,
              DECEMBER_2022,
              JANUARY_2024,
              FEBRUARY_2024,
              MARCH_2024,
              APRIL_2024,
              MAY_2024,
              JUNE_2024,
              JULY_2024,
              AUGUST_2024,
              SEPTEMBER_2024,
              OCTOBER_2024,
              NOVEMBER_2024,
              DECEMBER_2024,
              JANUARY_2025,
              FEBRUARY_2025,
              MARCH_2025,
            ],
          },
          {
            obligation: DECLARACION_DE_PROVEEDORES_DE_IVA,
            periods: [
              JUNE_2024,
              JULY_2024,
              AUGUST_2024,
              SEPTEMBER_2024,
              OCTOBER_2024,
              NOVEMBER_2024,
              DECEMBER_2024,
            ],
          },
          {
            obligation: PAGO_DEFINITIVO_MENSUAL_DE_IVA,
            periods: [
              SEPTEMBER_2022,
              OCTOBER_2022,
              NOVEMBER_2022,
              DECEMBER_2022,
              JANUARY_2023,
              FEBRUARY_2023,
              MARCH_2023,
              APRIL_2023,
              MAY_2023,
              AUGUST_2023,
              SEPTEMBER_2023,
              OCTOBER_2023,
              NOVEMBER_2023,
              DECEMBER_2023,
              JANUARY_2024,
              FEBRUARY_2024,
              MARCH_2024,
              APRIL_2024,
              MAY_2024,
              JUNE_2024,
              JULY_2024,
              AUGUST_2024,
              SEPTEMBER_2024,
              OCTOBER_2024,
              NOVEMBER_2024,
              DECEMBER_2024,
              JANUARY_2025,
              FEBRUARY_2025,
              MARCH_2025,
            ],
          },
        ],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();
    const result = extractDataFromPdf.execute(rfc, pdfText2Page);

    expect(result).toStrictEqual(expectedData);
  });

  it('should return the expected data when given a pdf with Bimestral data', () => {
    expect.hasAssertions();

    const rfc = 'TOOJ8912234NA';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: TOOJ8912234NA_ORIGINAL_STRING,
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [
          {
            obligation: 'Pago definitivo bimestral del RIF',
            periods: [
              BIMESTER_1_2021,
              BIMESTER_2_2021,
              BIMESTER_3_2021,
              BIMESTER_4_2021,
              BIMESTER_5_2021,
              BIMESTER_6_2021,
            ],
          },
          {
            obligation: 'Pago definitivo bimestral de IVA.',
            periods: [
              BIMESTER_1_2021,
              BIMESTER_2_2021,
              BIMESTER_3_2021,
              BIMESTER_4_2021,
              BIMESTER_5_2021,
              BIMESTER_6_2021,
            ],
          },
        ],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();
    const result = extractDataFromPdf.execute(rfc, pdfTextBimestral);

    expect(result).toStrictEqual(expectedData);
  });

  it('should return the expected data when given a pdf with Trimestral data', () => {
    expect.hasAssertions();

    const rfc = 'MTR160311495';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: '||AEQJ010419C83|25NC8449234|02-06-2025|N||00001088888800000031||',
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [
          {
            obligation:
              'Declaración informativa de 50 principales clientes y proveedores de IEPS. TRIMESTRAL',
            periods: [TRIMESTER_1_2025],
          },
        ],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();
    const result = extractDataFromPdf.execute(rfc, pdfTextTrimestral);

    expect(result).toStrictEqual(expectedData);
  });

  it('should return the expected data when given a pdf with Annual data', () => {
    expect.hasAssertions();

    const rfc = 'MTR160311495';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: '||AER150304CR0|25NC8313624|01-06-2025|N||00001088888800000031||',
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [
          {
            obligation: 'Declaración anual de ISR del ejercicio Personas morales.',
            periods: [ANNUAL_2022, ANNUAL_2024],
          },
        ],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();
    const result = extractDataFromPdf.execute(rfc, pdfTextAnnualObligations);

    expect(result).toStrictEqual(expectedData);
  });

  it('should return the expected data when given a pdf with monthly, bimestral and annual obligation data', () => {
    expect.hasAssertions();

    const rfc = 'MTR160311495';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: TOOJ8912234NA_ORIGINAL_STRING,
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [
          {
            obligation: 'Declaración anual de ISR. Personas Físicas.',
            periods: [ANNUAL_2022, ANNUAL_2023, ANNUAL_2024],
          },
          {
            obligation:
              'Pago provisional mensual de ISR por actividades empresariales. Régimen de Actividades Empresariales y Profesionales',
            periods: [
              JANUARY_2022,
              FEBRUARY_2022,
              MARCH_2022,
              APRIL_2022,
              MAY_2022,
              JUNE_2022,
              JULY_2022,
              AUGUST_2022,
              SEPTEMBER_2022,
              OCTOBER_2022,
              NOVEMBER_2022,
              DECEMBER_2022,
              JANUARY_2023,
              FEBRUARY_2023,
              MARCH_2023,
              APRIL_2023,
              MAY_2023,
              JUNE_2023,
              JULY_2023,
              AUGUST_2023,
              SEPTEMBER_2023,
              OCTOBER_2023,
              NOVEMBER_2023,
              DECEMBER_2023,
              JANUARY_2024,
              FEBRUARY_2024,
              MARCH_2024,
              APRIL_2024,
              MAY_2024,
              JUNE_2024,
              JULY_2024,
              AUGUST_2024,
              SEPTEMBER_2024,
              OCTOBER_2024,
              NOVEMBER_2024,
              DECEMBER_2024,
              JANUARY_2025,
              FEBRUARY_2025,
              MARCH_2025,
            ],
          },
          {
            obligation: 'Pago definitivo bimestral del RIF',
            periods: [
              BIMESTER_1_2021,
              BIMESTER_2_2021,
              BIMESTER_3_2021,
              BIMESTER_4_2021,
              BIMESTER_5_2021,
              BIMESTER_6_2021,
            ],
          },
          {
            obligation: 'Pago definitivo bimestral de IVA.',
            periods: [
              BIMESTER_1_2021,
              BIMESTER_2_2021,
              BIMESTER_3_2021,
              BIMESTER_4_2021,
              BIMESTER_5_2021,
              BIMESTER_6_2021,
            ],
          },
          {
            obligation: PAGO_DEFINITIVO_MENSUAL_DE_IVA,
            periods: [
              JANUARY_2022,
              FEBRUARY_2022,
              MARCH_2022,
              APRIL_2022,
              MAY_2022,
              JUNE_2022,
              JULY_2022,
              AUGUST_2022,
              SEPTEMBER_2022,
              OCTOBER_2022,
              NOVEMBER_2022,
              DECEMBER_2022,
              JANUARY_2023,
              FEBRUARY_2023,
              MARCH_2023,
              APRIL_2023,
              MAY_2023,
              JUNE_2023,
              JULY_2023,
              AUGUST_2023,
              SEPTEMBER_2023,
              OCTOBER_2023,
              NOVEMBER_2023,
              DECEMBER_2023,
              JANUARY_2024,
              FEBRUARY_2024,
              MARCH_2024,
              APRIL_2024,
              MAY_2024,
              JUNE_2024,
              JULY_2024,
              AUGUST_2024,
              SEPTEMBER_2024,
              OCTOBER_2024,
              NOVEMBER_2024,
              DECEMBER_2024,
              JANUARY_2025,
              FEBRUARY_2025,
              MARCH_2025,
            ],
          },
        ],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();
    const result = extractDataFromPdf.execute(rfc, pdfTextMonthlyBimestralAnualObligations);

    expect(result).toStrictEqual(expectedData);
  });

  it('should return the expected data with Semester', () => {
    expect.hasAssertions();

    const rfc = 'CIJG870721HA5';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: TOOJ8912234NA_ORIGINAL_STRING,
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [
          {
            obligation:
              'Declaración informativa de IEPS trasladado. Contribuyentes que produzcan o enajenen vinos de mesa SEMESTRAL',
            periods: [SEMESTER_1_2020, SEMESTER_2_2020],
          },
        ],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();
    const result = extractDataFromPdf.execute(rfc, pdfToTextCaseNotFound2Semester);

    expect(result).toStrictEqual(expectedData);
  });

  it('should return the expected data when given a complete pdf data', () => {
    expect.hasAssertions();

    const rfc = 'AAB210325FH5';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: AER150304CR0_ORIGINAL_STRING,
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [
          {
            obligation: ENTERO_DE_RETENCIONES_MENSUALES_DE_ISR,
            periods: [
              OCTOBER_2022,
              NOVEMBER_2022,
              DECEMBER_2022,
              JANUARY_2025,
              FEBRUARY_2025,
              MARCH_2025,
            ],
          },
          {
            obligation: 'Entero de retenciones mensuales de ISR por ingresos asimilados a salarios',
            periods: [OCTOBER_2022, NOVEMBER_2022, DECEMBER_2022],
          },
          {
            obligation: 'Pago provisional mensual de ISR personas morales régimen general',
            periods: [
              OCTOBER_2022,
              NOVEMBER_2022,
              DECEMBER_2022,
              JANUARY_2024,
              FEBRUARY_2024,
              MARCH_2024,
              APRIL_2024,
              MAY_2024,
              JUNE_2024,
              JULY_2024,
              AUGUST_2024,
              SEPTEMBER_2024,
              OCTOBER_2024,
              NOVEMBER_2024,
              DECEMBER_2024,
              JANUARY_2025,
              FEBRUARY_2025,
              MARCH_2025,
            ],
          },
          {
            obligation: 'Declaración anual de ISR del ejercicio Personas morales.',
            periods: [ANNUAL_2022, ANNUAL_2024],
          },
          {
            obligation: 'Declaración de proveedores de IVA',
            periods: [
              JUNE_2024,
              JULY_2024,
              AUGUST_2024,
              SEPTEMBER_2024,
              OCTOBER_2024,
              NOVEMBER_2024,
              DECEMBER_2024,
            ],
          },
          {
            obligation: PAGO_DEFINITIVO_MENSUAL_DE_IVA,
            periods: [
              SEPTEMBER_2022,
              OCTOBER_2022,
              NOVEMBER_2022,
              DECEMBER_2022,
              JANUARY_2023,
              FEBRUARY_2023,
              MARCH_2023,
              APRIL_2023,
              MAY_2023,
              AUGUST_2023,
              SEPTEMBER_2023,
              OCTOBER_2023,
              NOVEMBER_2023,
              DECEMBER_2023,
              JANUARY_2024,
              FEBRUARY_2024,
              MARCH_2024,
              APRIL_2024,
              MAY_2024,
              JUNE_2024,
              JULY_2024,
              AUGUST_2024,
              SEPTEMBER_2024,
              OCTOBER_2024,
              NOVEMBER_2024,
              DECEMBER_2024,
              JANUARY_2025,
              FEBRUARY_2025,
              MARCH_2025,
            ],
          },
        ],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();
    const result = extractDataFromPdf.execute(rfc, pdfTextFirstCompleteDocument);

    expect(result).toStrictEqual(expectedData);
  });
});
