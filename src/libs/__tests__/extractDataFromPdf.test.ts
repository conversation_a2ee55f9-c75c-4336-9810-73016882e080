import { describe, expect, it } from '@jest/globals';

import { ExtractDataFromPdf } from '!libs/extractDataFromPdf';

describe('extractDataFromPdf', () => {
  it('should return the expected data when given a pdfArray', () => {
    expect.hasAssertions();

    const mockPdfText = `
      contenidos en la Resolución Miscelánea Fiscal vigente. Por lo que se emite esta opinión del cumplimiento de obligaciones fiscales, en sentido
      POSITIVO.
      Folio                                                                Clave de R.F.C.
      23NA3107220                                                                   MCO220930R9A
      Cadena Original
      ||MCO220930R9A|23NA3107220|25-01-2023|P||00001088888800000031||
    `;
    const rfc = 'MCO220930R9A';
    const expectedData = {
      opinion: 'POSITIVO',
      cadenaOriginal: '||MCO220930R9A|23NA3107220|25-01-2023|P||00001088888800000031||',
      creditNumber: '',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {},
    };

    const extractDataFromPdf = new ExtractDataFromPdf();

    expect(extractDataFromPdf.execute(rfc, mockPdfText)).toStrictEqual(expectedData);
  });

  it('should return the expected data when given a pdfArray with a negative opinion', () => {
    expect.hasAssertions();

    const mockPdfText = `
      contenidos en la Resolución Miscelánea Fiscal vigente. Por lo que se emite esta opinión del cumplimiento de obligaciones fiscales, en sentido
      NEGATIVO.
      Se ubican los siguientes créditos fiscales firmes o no garantizados a su cargo:
      425570818
      Cadena Original
      ||MDI790709766|21NI7464498|23-12-2021|N||00001088888800000031||
      Sello Digital
      Folio                                                                Clave de R.F.C.
      23NA3107220                                                                   MCO220930R9A
    `;
    const rfc = 'MCO220930R9A';
    const expectedData = {
      opinion: 'NEGATIVO',
      cadenaOriginal: '||MDI790709766|21NI7464498|23-12-2021|N||00001088888800000031||',
      creditNumber: '425570818',
      rfc,
      date: new Date().toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
      detail: {
        complianceObligations: [],
      },
    };

    const extractDataFromPdf = new ExtractDataFromPdf();

    expect(extractDataFromPdf.execute(rfc, mockPdfText)).toStrictEqual(expectedData);
  });
});
