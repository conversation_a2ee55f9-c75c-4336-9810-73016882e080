import * as crypto from 'crypto';

const { ENCRYPTION_KEY } = process.env; // Must be 256 bits (32 characters)

export const decrypt = (text = ''): string => {
  const textParts = text.split(':') || [];
  const iv = Buffer.from(textParts.shift() || '', 'hex');
  const encryptedText = Buffer.from(textParts.join(':') || '', 'hex');
  const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY || ''), iv);
  let decrypted = decipher.update(encryptedText);

  decrypted = Buffer.concat([decrypted, decipher.final()]);

  return decrypted.toString();
};
