import { Logger } from '@xepelinapp/blackops-commons-logger';
import { callWebHook, WebhookType } from '@xepelinapp/blackops-commons-webhook';

import { SendWebhookError } from '!libs/errors';

export const sendWebhook = async ({
  webhook,
  data,
  identifier,
  logger,
}: {
  webhook: WebhookType;
  data: Record<string, unknown>;
  identifier: string;
  logger: Logger;
}): Promise<void> => {
  try {
    const webhookData = {
      url: `${webhook.url}`,
      ...(webhook.headers && { headers: webhook.headers }),
      ...(webhook.extras && { extras: webhook.extras }),
      data: { ...data },
    };

    logger.info({
      message: '----> Webhook data',
      data: webhookData,
    });

    await callWebHook(webhookData);
    logger.debug({
      message: '----> Successfully webhook sent',
    });
  } catch (error) {
    logger.debug({
      message: 'Error sending webhook',
    });
    logger.error(error);
    throw new SendWebhookError(identifier);
  }
};
