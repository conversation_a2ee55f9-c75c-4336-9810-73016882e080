import fs from 'fs';
import path from 'path';

import { S3 } from '@xepelinapp/blackops-commons-aws-lambda';
import { Logger } from '@xepelinapp/blackops-commons-logger';

import { UploadToS3Error } from '!libs/errors';
import { PdfData } from '!types/pdfData';

export class UploadToS3 {
  private logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async execute(user: string, pdfData: PdfData): Promise<void> {
    try {
      const bucketName = process.env.AWS_S3_BUCKET_NAME || '';
      const fileName = `${user}.pdf`;
      const filePath = path.join('/tmp', fileName);
      const data = fs.readFileSync(filePath);

      const jsonData = { user, ...pdfData };
      const jsonName = `${user}.json`;
      const jsonString = JSON.stringify(jsonData);

      const jsonBuffer = Buffer.from(jsonString);

      const s3service = new S3();
      await s3service.putS3Object({ bucketName, path: fileName, data });
      await s3service.putS3Object({ bucketName, path: jsonName, data: jsonBuffer });

      this.logger.debug({
        message: '----> Successfully uploaded to S3',
      });
    } catch (error) {
      this.logger.debug({
        message: 'Error uploading to S3',
      });
      this.logger.error(error);
      throw new UploadToS3Error(user);
    }
  }
}
