import fs from 'fs';

import { Logger } from '@xepelinapp/blackops-commons-logger';
import { sendRequest, HttpRequestConfig } from '@xepelinapp/blackops-commons-send-request';

import { EmptyDocumentError, PdfParsingError } from '!libs/errors';

export class ParsePdf {
  private logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  public async execute(user: string): Promise<string> {
    const filePath = `/tmp/${user}.pdf`;
    try {
      if (!fs.existsSync(filePath)) {
        throw new EmptyDocumentError(user);
      }
      const { PDF_PARSER_URL: pdfParserUrl } = process.env;
      const pdfBuffer = fs.readFileSync(filePath);
      const config: HttpRequestConfig = {
        method: 'post',
        url: `${pdfParserUrl}/pypdf2`,
        headers: {
          'Content-Type': 'application/octet-stream',
        },
        data: pdfBuffer,
      };

      const response = await sendRequest<{ content: string }>(config, true);

      const { content = '' } = response.data;
      this.logger.debug({
        message: '----> Successfully parsed PDF',
      });
      return content;
    } catch (error) {
      this.logger.debug({
        message: 'Error parsing PDF',
      });
      this.logger.error(error);
      throw new PdfParsingError(user);
    }
  }
}
