const { pathsToModuleNameMapper } = require('ts-jest');
const { compilerOptions } = require('./tsconfig');

module.exports = {
  roots: ['src'],
  testMatch: ['**/__tests__/**/*.test.+(ts|tsx|js)'],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  verbose: true,
  preset: 'ts-jest',
  modulePaths: [compilerOptions.baseUrl],
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, { prefix: '<rootDir>/' }),
  coverageThreshold: {
    global: {
      branches: 60,
      functions: 92,
      lines: 80,
      statements: 99,
    },
  },
};

process.env = Object.assign(process.env, {
  AWS_SQS_QUEUE_URL: 'test-queue-name',
  AWS_SQS_REQUEST_QUEUE_URL: 'test-queue-name',
});
