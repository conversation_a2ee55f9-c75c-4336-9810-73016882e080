{"name": "xepelin-sls-sat69", "version": "1.0.0", "description": "Sat69", "scripts": {"build": "node ./esbuild.mjs", "invoke:checkSat69": "npx ts-node src/handlerLocal.ts checkSat69", "lint:check": "prettier --check ./src && tsc --noEmit && eslint '*/**/*.{js,ts}'", "test": "NODE_ENV=test jest --forceExit --detectOpenHandles", "test:coverage": "NODE_ENV=test jest --forceExit --collect-coverage --detectOpenHandles", "swagger:run": "docker run --rm -p 8080:8080 -e 'SWAGGER_JSON=/app/swagger.yml' -v $(pwd)/swagger/definitions:/app swaggerapi/swagger-ui:v4.14.0", "devel": "sls offline", "test:coverage-centralizado": "jest --coverage"}, "engines": {"node": "20.x.x"}, "devDependencies": {"@pulumi/aws": "^6.72.0", "@pulumi/pulumi": "^3.156.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.11", "@tsconfig/node16": "^16.1.3", "@types/aws-lambda": "^8.10.147", "@types/jest": "^29.5.14", "@types/node": "^22.13.10", "@types/puppeteer": "^7.0.4", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "@xepelinapp/blackops-commons-pulumi-utils": "^4.6.0", "compressing": "^1.10.1", "esbuild": "^0.19.4", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^28.6.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-sonarjs": "^0.21.0", "jest": "^29.7.0", "prettier": "^3.5.3", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2"}, "dependencies": {"@supercharge/promise-pool": "^3.2.0", "@xepelinapp/blackops-big-brother-lib": "^2.0.0", "@xepelinapp/blackops-commons-aws-lambda": "^4.6.0", "@xepelinapp/blackops-commons-credentials-manager-lambda": "^4.6.0", "@xepelinapp/blackops-commons-logger": "^4.6.0", "@xepelinapp/blackops-commons-puppeteer": "^4.6.0", "@xepelinapp/blackops-commons-send-request": "^4.6.0", "@xepelinapp/blackops-commons-webhook": "^4.6.0", "ajv": "^8.17.1"}, "author": "Blackops team (<EMAIL>)"}