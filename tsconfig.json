{"$schema": "https://json.schemastore.org/tsconfig", "display": "Node 20", "compilerOptions": {"lib": ["es2021", "dom"], "module": "commonjs", "target": "es2021", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "moduleResolution": "node", "noUnusedLocals": true, "noUnusedParameters": true, "removeComments": true, "sourceMap": true, "paths": {"!dtos/*": ["src/dtos/*"], "!libs/*": ["src/libs/*"], "!services/*": ["src/services/*"], "!mocks/*": ["src/mocks/*"], "!schemas/*": ["src/schemas/*"], "!types/*": ["src/types/*"], "!useCases/*": ["src/useCases/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules/**/*", ".webpack/**/*", "_warmup/**/*", ".vscode/**/*"], "ts-node": {"require": ["tsconfig-paths/register"]}}