import { config, iam, bigBrother, datadog } from '@xepelinapp/blackops-commons-pulumi-utils';
import { Envs } from '../utils/config';

const { envs } = config.getConfig<Envs>();

type PolicyIamRole = typeof iam.getIamForLambda.arguments;

const dataDogConfig = new datadog.DatadogConfig(config.getConfig().environment);

export const policyIamRole: PolicyIamRole[] = [
  {
    Effect: 'Allow',
    Action: [
      'ec2:DescribeNetworkInterfaces',
      'ec2:CreateNetworkInterface',
      'ec2:DeleteNetworkInterface',
      'ec2:DescribeInstances',
      'ec2:AttachNetworkInterface',
    ],
    Resource: ['*'],
  },
  {
    Effect: 'Allow',
    Resource: ['arn:aws:s3:::xepelin-chromium-upload', 'arn:aws:s3:::xepelin-chromium-upload/*'],
    Action: ['s3:*'],
  },
  {
    Sid: 'SQSStatement',
    Effect: 'Allow',
    Action: [
      'sqs:SendMessage',
      'sqs:ReceiveMessage',
      'sqs:DeleteMessage',
      'sqs:GetQueueAttributes',
      'sqs:GetQueueUrl',
      'sqs:ListQueues',
      's3:PutObject',
      's3:GetObject',
    ],
    Resource: ['*'],
  },
  {
    Sid: 'SecretStatement',
    Effect: 'Allow',
    Action: ['secretsmanager:GetSecretValue'],
    Resource: [envs.SECRETS_ARN],
  },
  {
    Sid: 'KMSStatement',
    Effect: 'Allow',
    Action: ['kms:Decrypt'],
    Resource: [envs.SECRET_KMS_ARN],
  },
  ...bigBrother.getPolicy(config.getConfig().environment, 'mx'),
  ...dataDogConfig.getPolicy(envs.DD_API_KEY_SECRET_ARN),
];
