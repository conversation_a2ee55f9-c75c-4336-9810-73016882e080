import { config, iam, lambda, datadog } from '@xepelinapp/blackops-commons-pulumi-utils';
import { fetchQueue } from '../../queues/fetchQueue';
import { policyIamRole } from '../policyRole';
import { Envs } from '../../utils/config';

const name = 'checkSat69';

const { envs } = config.getConfig<Envs>();

export const checkSat69RoleLambda = iam.getIamForLambda({
  name,
  statements: [
    ...policyIamRole,
    {
      Effect: 'Allow',
      Action: ['s3:PutObject', 's3:GetObject'],
      Resource: ['*'],
    },
  ],
});

const dataDogConfig = new datadog.DatadogConfig(config.getConfig().environment);

const prdPuppeteerLayer = 'arn:aws:lambda:us-east-1:404610637048:layer:blackops-puppeteer-layer:2';
const devPuppeteerLayer = 'arn:aws:lambda:us-east-1:857337506027:layer:blackops-puppeteer-layer:13';

export const checkSat69Lambda = lambda.createLambda({
  name,
  folder: '../dist/checkSat69.zip',
  handler: dataDogConfig.getHandler('src/handler.checkSat69'),
  iamRole: checkSat69RoleLambda,
  description: 'Lambda that scrapes the Sat69',
  timeout: 600,
  memorySize: 1024,
  reservedConcurrentExecutions: 50,
  envs: {
    SERVICE_NAME: name,
    ...dataDogConfig.getEnvs('src/handler.checkSat69', 'sat69'),
    ...envs,
  },
  layerArns: [
    'arn:aws:lambda:us-east-1:299305025888:layer:env-from-secrets-manager:17',
    config.getConfig().environment === 'prd' ? prdPuppeteerLayer : devPuppeteerLayer,
    ...dataDogConfig.getLayers(),
  ],
});

lambda.createSubscriptionSQS({
  name,
  lambdaFn: checkSat69Lambda,
  queue: fetchQueue,
  batchSize: 1,
  functionResponseTypes: ['ReportBatchItemFailures'],
  maximumConcurrency: 50,
});
