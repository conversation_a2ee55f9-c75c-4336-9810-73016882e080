import { config, iam, lambda } from '@xepelinapp/blackops-commons-pulumi-utils';
import { policyIamRole } from '../policyRole';
import { Envs } from '../../utils/config';

const name = 'retrievePdf';

const { envs } = config.getConfig<Envs>();

export const retrievePdfRoleLambda = iam.getIamForLambda({
  name,
  statements: policyIamRole,
});

export const retrievePdfLambda = lambda.createLambda({
  name,
  folder: '../dist/retrievePdf.zip',
  handler: 'src/handler.retrievePdf',
  iamRole: retrievePdfRoleLambda,
  envs: {
    SERVICE_NAME: name,
    ...envs,
  },
  layerArns: ['arn:aws:lambda:us-east-1:299305025888:layer:env-from-secrets-manager:17'],
});

iam.createPermissionForApiGateway({
  name,
  lambdaFn: retrievePdfLambda,
  sourceArn: config.getConfig().internalSourceArn,
});
