import { config, iam, lambda } from '@xepelinapp/blackops-commons-pulumi-utils';
import { policyIamRole } from '../policyRole';
import { Envs } from '../../utils/config';

const name = 'triggerEnqueue';

const { envs } = config.getConfig<Envs>();

export const triggerEnqueueRoleLambda = iam.getIamForLambda({
  name,
  statements: policyIamRole,
});

export const triggerEnqueueLambda = lambda.createLambda({
  name,
  folder: '../dist/triggerEnqueue.zip',
  handler: 'src/handler.triggerEnqueue',
  iamRole: triggerEnqueueRoleLambda,
  timeout: 900,
  envs: {
    SERVICE_NAME: name,
    ...envs,
  },
  layerArns: ['arn:aws:lambda:us-east-1:299305025888:layer:env-from-secrets-manager:17'],
});

iam.createPermissionForApiGateway({
  name,
  lambdaFn: triggerEnqueueLambda,
  sourceArn: config.getConfig().internalSourceArn,
});
