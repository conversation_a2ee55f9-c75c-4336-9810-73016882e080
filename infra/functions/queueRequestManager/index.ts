import { config, iam, lambda } from '@xepelinapp/blackops-commons-pulumi-utils';
import { requestQueue } from '../../queues/requestQueue';
import { policyIamRole } from '../policyRole';
import { Envs } from '../../utils/config';

const name = 'queueRequestManager';

const { envs } = config.getConfig<Envs>();

export const queueRequestManagerRoleLambda = iam.getIamForLambda({
  name,
  statements: policyIamRole,
});

export const queueRequestManagerLambda = lambda.createLambda({
  name,
  folder: '../dist/queueRequestManager.zip',
  handler: 'src/handler.queueRequestManager',
  iamRole: queueRequestManagerRoleLambda,
  timeout: 900,
  envs: {
    SERVICE_NAME: name,
    ...envs,
  },
  layerArns: ['arn:aws:lambda:us-east-1:299305025888:layer:env-from-secrets-manager:17'],
});

lambda.createSubscriptionSQS({
  name,
  lambdaFn: queueRequestManagerLambda,
  queue: requestQueue,
  batchSize: 1,
});
