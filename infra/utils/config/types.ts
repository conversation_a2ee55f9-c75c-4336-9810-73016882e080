export type Country = 'CL' | 'MX' | 'GLOBAL';

export type Envs = {
  SECRET_KMS_ARN: string;
  SECRETS_ARN: string;
  AWS_SQS_QUEUE_URL: string;
  AWS_SQS_REQUEST_QUEUE_URL: string;
  AWS_NODEJS_CONNECTION_REUSE_ENABLED: string;
  NODE_OPTIONS: string;
  AWS_S3_BUCKET_NAME: string;
  PUPPETEER_TIMEOUT: string;
  PDF_PARSER_URL: string;
  CREDENTIALS_MANAGER_CONCURRENCY_RATE: string;
  SERVICE_UUID: string;
  PROXY_AGENT: string;
  CREDENTIALS_MANAGER_URL: string;
  DD_API_KEY_SECRET_ARN: string;
  DB_NAME: string;
  DD_SITE: string;
};
