encryptionsalt: v1:oxZsDSZ3WoM=:v1:vq0ppDPe8gcTRPnH:yiOvYiKqObSZSludCDWdCn2bPENugg==
config:
  project:awsAccountId: ************
  project:country: mx
  project:environment: dev
  aws:region: us-east-1
  api-platform:internal-arn: arn:aws:execute-api:us-east-1:************:ztk99zvxsb/*/*
  network:vpcId: vpc-03d3e94662480ea18
  network:cidr: 10.110.0.0/16
  network:subnetIds:
    - subnet-059ec210a98e067f5
    - subnet-0fadf28469901b434
    - subnet-05bebbaad74e07ccc
  stack:envs:
    SECRETS_ARN: 'arn:aws:secretsmanager:us-east-1:************:secret:dev/MX/app/sat69-scrapper-9wu5gi'
    SECRET_KMS_ARN: 'arn:aws:kms:us-east-1:************:key/mrk-09382430661c45c89ee7fbf033660992'
    AWS_LAMBDA_EXEC_WRAPPER: /opt/wrapper-env-from-secrets-manager
    AWS_SQS_QUEUE_URL: 'https://sqs.us-east-1.amazonaws.com/************/dev-sat69-scrapper-mx-fetchQueue'
    AWS_SQS_REQUEST_QUEUE_URL: 'https://sqs.us-east-1.amazonaws.com/************/dev-sat69-scrapper-mx-requestQueue'
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: '1'
    NODE_OPTIONS: '--enable-source-maps --stack-trace-limit=1000'
    NODE_ENV: 'development'
    AWS_S3_BUCKET_NAME: 'dev-sat69-scrapper-mx-compliance-opinion'
    PUPPETEER_TIMEOUT: '0'
    PDF_PARSER_URL: 'https://dev.internal.api.xepelin/mx/v1/blackops/pdf-parser/parse-document'
    CREDENTIALS_MANAGER_CONCURRENCY_RATE: '25'
    SERVICE_UUID: 'aeb9d80b-c9f9-4e9a-b859-fb173c868c30'
    PROXY_AGENT: 'http://auto:<EMAIL>:8000'
    CREDENTIALS_MANAGER_URL: 'https://blackops-credentials-manager.staging.api.mx.xepel.in'
    LOG_FORMAT: json
    LOG_LEVEL: debug
