import { config } from '@xepelinapp/blackops-commons-pulumi-utils';

import { all } from '@pulumi/pulumi';
import { retrievePdfLambda } from './functions/retrievePdf';
import { triggerEnqueueLambda } from './functions/triggerEnqueue';
import { checkSat69Lambda } from './functions/checkSat69';
import { queueRequestManagerLambda } from './functions/queueRequestManager';
import { sat69S3 } from './s3/sat69S3/index';

export const checkSat69LambdaArn = checkSat69Lambda.arn;
export const queueRequestManagerLambdaArn = queueRequestManagerLambda.arn;
export const sat69S3Arn = sat69S3.arn;

export const apiPlatformOutput = all([retrievePdfLambda.arn, triggerEnqueueLambda.arn]).apply(
  ([retrievePdfLambdaArn, triggerEnqueueLambdaArn]) => [
    {
      name: 'retrieve-pdf',
      api: 'internal',
      environment: config.getConfig().environment,
      country: 'MX',
      integration: 'Lambda',
      resource_arn: retrievePdfLambdaArn,
      method: 'GET',
      path: '/sat69-scrapper/retrieve-pdf/{taxID}',
      group_type: 'team',
      group_identifier: 'blackops',
      owner: 'blackops',
      authorizer: false,
      slack: '*****************************************************************************',
    },
    {
      name: 'trigger-enqueue',
      api: 'internal',
      environment: config.getConfig().environment,
      country: 'MX',
      integration: 'Lambda',
      resource_arn: triggerEnqueueLambdaArn,
      method: 'POST',
      path: '/sat69-scrapper/trigger-enqueue',
      group_type: 'team',
      group_identifier: 'blackops',
      owner: 'blackops',
      authorizer: false,
      slack: '*****************************************************************************',
    },
  ]
);
