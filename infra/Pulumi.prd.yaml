config:
  project:awsAccountId: ************
  project:country: mx
  project:environment: prd
  aws:region: us-east-1
  api-platform:internal-arn: arn:aws:execute-api:us-east-1:************:wirtgvsk16/*/*
  network:vpcId: vpc-0f2f2a0a249c266d3
  network:cidr: 10.70.0.0/16
  network:subnetIds:
    - subnet-0e475152761cfa958
    - subnet-03dca61783003284e
    - subnet-0f7177c65335bf4e0
  stack:envs:
    SECRETS_ARN: 'arn:aws:secretsmanager:us-east-1:************:secret:prd/MX/app/sat69-scrapper-xamCuD'
    SECRET_KMS_ARN: arn:aws:kms:us-east-1:************:key/mrk-4e21d038f0834c65b3d1226839d0c79c
    AWS_LAMBDA_EXEC_WRAPPER: /opt/wrapper-env-from-secrets-manager
    AWS_SQS_QUEUE_URL: 'https://sqs.us-east-1.amazonaws.com/************/prd-sat69-scrapper-mx-fetchQueue'
    AWS_SQS_REQUEST_QUEUE_URL: 'https://sqs.us-east-1.amazonaws.com/************/prd-sat69-scrapper-mx-requestQueue'
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: '1'
    NODE_OPTIONS: '--enable-source-maps --stack-trace-limit=1000'
    NODE_ENV: 'production'
    AWS_S3_BUCKET_NAME: 'prd-sat69-scrapper-mx-compliance-opinion'
    PUPPETEER_TIMEOUT: '0'
    PDF_PARSER_URL: 'https://internal.api.xepelin/mx/v1/blackops/pdf-parser/parse-document'
    CREDENTIALS_MANAGER_CONCURRENCY_RATE: '25'
    SERVICE_UUID: '2ca9441f-c43d-4969-8c5e-b881842545c8'
    PROXY_AGENT: 'http://auto:<EMAIL>:8000'
    CREDENTIALS_MANAGER_URL: 'https://blackops-credentials-manager.api.mx.xepelin.com'
    LOG_FORMAT: json
    LOG_LEVEL: info
    DD_SITE: datadoghq.com
    DD_API_KEY_SECRET_ARN: 'arn:aws:secretsmanager:us-east-1:************:secret:prd/MX/service/Datadog-1Jl1rk'
