# SonarCloud organization key
sonar.organization=xepelinapp

# Project details
sonar.projectKey=xepelinapp_blackops-sat69-scrapper

# Source files and tests
sonar.sources=src
sonar.tests=src
sonar.test.inclusions=**/*test*.js,**/*spec*.js
sonar.coverage.exclusions=**/__tests__/**

# Coverage report path
sonar.javascript.lcov.reportPaths=./coverage/lcov.info

# Additional properties (if needed)
# sonar.language=js
# sonar.sourceEncoding=UTF-8