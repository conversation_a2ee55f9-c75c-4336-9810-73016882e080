import fs from 'node:fs';
import compressing from 'compressing';
import { build } from 'esbuild';

const buildProject = async ({ entry, name }) => {
  await build({
    entryPoints: [entry],
    outfile: `./dist/${name}/src/handler.js`,
    bundle: true,
    platform: 'node',
    sourcemap: true,
    sourcesContent: false,
    target: ['es2020'],
    logLevel: 'debug',
    external: [
      '@sparticuz/chromium',
      '@xepelinapp/blackops-commons-puppeteer',
      'puppeteer-extra-plugin-stealth',
      'puppeteer-core',
      'puppeteer',
    ],
  }).catch(console.log);

  await compressing.zip
    .compressDir(`./dist/${name}/src`, `./dist/${name}.zip`)
    .catch(console.error);

  fs.rmSync(`./dist/${name}`, { force: true, recursive: true });
};

const preBuild = async () => {
  fs.rmSync(`./dist`, { force: true, recursive: true });
  fs.mkdirSync('./dist');
};

const buildProjects = async () => {
  await Promise.all([
    buildProject({
      entry: './src/useCases/checkSat69/checkSat69.handler',
      name: 'checkSat69',
    }),

    buildProject({
      entry: './src/useCases/retrievePdf/retrievePdf.handler',
      name: 'retrievePdf',
    }),

    buildProject({
      entry: './src/useCases/triggerEnqueue/triggerEnqueue.handler',
      name: 'triggerEnqueue',
    }),
    buildProject({
      entry: './src/useCases/queueRequestManager/queueRequestManager.handler',
      name: 'queueRequestManager',
    }),
  ]);
};

const buildAll = async () => {
  await preBuild();

  await buildProjects();
};

buildAll();
